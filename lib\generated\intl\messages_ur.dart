// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ur locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ur';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts": MessageLookupByLibrary.simpleMessage(
            "مترجم کو مصنوعی ذہانت استعمال کرتے ہوئے متن کا خودکار ترجمہ کرنے اور منتخب زبان میں بھیجنے کی اجازت دیں۔"),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "مترجم کو مصنوعی ذہانت استعمال کرتے ہوئے آپ کی آڈیو ریکارڈنگ کا خودکار ترجمہ کرنے اور منتخب زبان میں بھیجنے کی اجازت دیں۔"),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "فون ایپس تک رسائی کی اجازت دیں۔"),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "مترجم کے ساتھ فوری اور ہموار ترجمے کے تجربے کا لطف اٹھائیں، جہاں آپ آسانی سے اور تیزی سے متن، الفاظ اور آڈیو ریکارڈنگ کو اپنی پسندیدہ زبان میں تبدیل کر سکتے ہیں۔ جدید مصنوعی ذہانت کی ٹیکنالوجیز کی بدولت جو اعلیٰ درستگی اور آرام دہ تجربہ کو یقینی بناتی ہیں۔"),
        "enable_camera": MessageLookupByLibrary.simpleMessage(
            "مترجم کو کیمرا استعمال کرنے کی اجازت دیں"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage(
            "مترجم کو مائیکروفون استعمال کرنے کی اجازت دیں"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "مترجم کو گیلری سے فائلیں درآمد کرنے کی اجازت دیں"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "مترجم کو گیلری سے تصاویر درآمد کرنے کی اجازت دیں"),
        "language": MessageLookupByLibrary.simpleMessage("زبان"),
        "live_translation":
            MessageLookupByLibrary.simpleMessage("یہاں ترجمہ لکھیں..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "مصنوعی ذہانت استعمال کرتے ہوئے آڈیو ریکارڈنگ کو ترجمہ شدہ ریکارڈنگ میں تبدیل کریں۔"),
        "name_app": MessageLookupByLibrary.simpleMessage("مترجم"),
        "search_language":
            MessageLookupByLibrary.simpleMessage("زبان تلاش کریں..."),
        "symbol_appears_on_the_screen": MessageLookupByLibrary.simpleMessage(
            "علامت اسکرین پر ظاہر ہوتی ہے"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "مصنوعی ذہانت کی طاقت کے ساتھ مترجم کے ساتھ فوری طور پر ترجمہ کریں۔"),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "اب متن کا ترجمہ کرنے کے لیے مترجم استعمال کریں۔")
      };
}
