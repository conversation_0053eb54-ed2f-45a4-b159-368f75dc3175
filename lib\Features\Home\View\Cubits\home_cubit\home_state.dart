part of 'home_cubit.dart';

@immutable
class HomeState {
  final String? languageFirstTranslate;
  final String? languageSecondTranslate;
  final bool isTutorialHomeShowed;
  final bool isTutorialFloatingShowed;
  final List<Locale>? locale ;


  const HomeState({
    this.languageFirstTranslate,
    this.languageSecondTranslate,
    this.isTutorialHomeShowed = false,
    this.isTutorialFloatingShowed = false,
     this.locale,
  });

  HomeState copyWith({
    String? languageFirstTranslate,
    String? languageSecondTranslate,
    bool? isTutorialHomeShowed,
    bool? isTutorialFloatingShowed,
    List<Locale>? locale,
  }) {
    return HomeState(
      languageFirstTranslate: languageFirstTranslate ?? this.languageFirstTranslate,
      languageSecondTranslate: languageSecondTranslate ?? this.languageSecondTranslate,
      isTutorialHomeShowed: isTutorialHomeShowed ?? this.isTutorialHomeShowed,
      isTutorialFloatingShowed: isTutorialFloatingShowed ?? this.isTutorialFloatingShowed,
      locale: locale ?? this.locale,
    );
  }
}
