package com.example.al_tarjuman.injection

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.graphics.Path
import android.graphics.Rect
import android.os.Build
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import androidx.annotation.RequiresApi

/**
 * Advanced gesture-based text injection using AccessibilityService gestures
 */
@RequiresApi(Build.VERSION_CODES.N)
class GestureInjectionService(private val accessibilityService: AccessibilityService) {
    
    companion object {
        private const val TAG = "GestureInjectionService"
        private const val GESTURE_DURATION = 100L
        private const val TAP_DURATION = 50L
        private const val LONG_PRESS_DURATION = 500L
    }
    
    /**
     * Inject text using gesture-based approach
     */
    fun injectTextWithGestures(
        inputField: AccessibilityNodeInfo,
        text: String,
        callback: (Boolean) -> Unit
    ) {
        try {
            val bounds = Rect()
            inputField.getBoundsInScreen(bounds)
            
            // Step 1: Tap to focus the input field
            tapOnField(bounds) { focusSuccess ->
                if (!focusSuccess) {
                    callback(false)
                    return@tapOnField
                }
                
                // Step 2: Long press to select all existing text
                longPressOnField(bounds) { selectSuccess ->
                    if (!selectSuccess) {
                        callback(false)
                        return@longPressOnField
                    }
                    
                    // Step 3: Use accessibility action to set text
                    val success = setTextDirectly(inputField, text)
                    callback(success)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Gesture injection failed", e)
            callback(false)
        }
    }
    
    /**
     * Perform a tap gesture on the input field
     */
    private fun tapOnField(bounds: Rect, callback: (Boolean) -> Unit) {
        val centerX = bounds.centerX().toFloat()
        val centerY = bounds.centerY().toFloat()
        
        val path = Path().apply {
            moveTo(centerX, centerY)
        }
        
        val gesture = GestureDescription.Builder()
            .addStroke(GestureDescription.StrokeDescription(path, 0, TAP_DURATION))
            .build()
        
        accessibilityService.dispatchGesture(gesture, object : AccessibilityService.GestureResultCallback() {
            override fun onCompleted(gestureDescription: GestureDescription?) {
                Log.d(TAG, "Tap gesture completed")
                // Wait a bit for the field to focus
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    callback(true)
                }, 100)
            }
            
            override fun onCancelled(gestureDescription: GestureDescription?) {
                Log.w(TAG, "Tap gesture cancelled")
                callback(false)
            }
        }, null)
    }
    
    /**
     * Perform a long press gesture to select all text
     */
    private fun longPressOnField(bounds: Rect, callback: (Boolean) -> Unit) {
        val centerX = bounds.centerX().toFloat()
        val centerY = bounds.centerY().toFloat()
        
        val path = Path().apply {
            moveTo(centerX, centerY)
        }
        
        val gesture = GestureDescription.Builder()
            .addStroke(GestureDescription.StrokeDescription(path, 0, LONG_PRESS_DURATION))
            .build()
        
        accessibilityService.dispatchGesture(gesture, object : AccessibilityService.GestureResultCallback() {
            override fun onCompleted(gestureDescription: GestureDescription?) {
                Log.d(TAG, "Long press gesture completed")
                // Wait for selection to complete
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    callback(true)
                }, 200)
            }
            
            override fun onCancelled(gestureDescription: GestureDescription?) {
                Log.w(TAG, "Long press gesture cancelled")
                callback(false)
            }
        }, null)
    }
    
    /**
     * Perform swipe gesture for text selection
     */
    fun performSwipeGesture(
        startX: Float,
        startY: Float,
        endX: Float,
        endY: Float,
        duration: Long = GESTURE_DURATION,
        callback: (Boolean) -> Unit
    ) {
        val path = Path().apply {
            moveTo(startX, startY)
            lineTo(endX, endY)
        }
        
        val gesture = GestureDescription.Builder()
            .addStroke(GestureDescription.StrokeDescription(path, 0, duration))
            .build()
        
        accessibilityService.dispatchGesture(gesture, object : AccessibilityService.GestureResultCallback() {
            override fun onCompleted(gestureDescription: GestureDescription?) {
                Log.d(TAG, "Swipe gesture completed")
                callback(true)
            }
            
            override fun onCancelled(gestureDescription: GestureDescription?) {
                Log.w(TAG, "Swipe gesture cancelled")
                callback(false)
            }
        }, null)
    }
    
    /**
     * Perform multi-touch gesture (e.g., pinch, zoom)
     */
    fun performMultiTouchGesture(
        paths: List<Path>,
        durations: List<Long>,
        callback: (Boolean) -> Unit
    ) {
        if (paths.size != durations.size) {
            callback(false)
            return
        }
        
        val gestureBuilder = GestureDescription.Builder()
        
        paths.forEachIndexed { index, path ->
            gestureBuilder.addStroke(
                GestureDescription.StrokeDescription(path, 0, durations[index])
            )
        }
        
        val gesture = gestureBuilder.build()
        
        accessibilityService.dispatchGesture(gesture, object : AccessibilityService.GestureResultCallback() {
            override fun onCompleted(gestureDescription: GestureDescription?) {
                Log.d(TAG, "Multi-touch gesture completed")
                callback(true)
            }
            
            override fun onCancelled(gestureDescription: GestureDescription?) {
                Log.w(TAG, "Multi-touch gesture cancelled")
                callback(false)
            }
        }, null)
    }
    
    /**
     * Simulate keyboard typing using gestures (for virtual keyboards)
     */
    fun simulateKeyboardTyping(
        text: String,
        keyboardBounds: Rect,
        callback: (Boolean) -> Unit
    ) {
        // This is a simplified implementation
        // In a real scenario, you'd need to map characters to keyboard positions
        
        val gestures = mutableListOf<GestureDescription>()
        var delay = 0L
        
        text.forEach { char ->
            val keyPosition = getKeyPosition(char, keyboardBounds)
            if (keyPosition != null) {
                val path = Path().apply {
                    moveTo(keyPosition.first, keyPosition.second)
                }
                
                val gesture = GestureDescription.Builder()
                    .addStroke(GestureDescription.StrokeDescription(path, delay, TAP_DURATION))
                    .build()
                
                gestures.add(gesture)
                delay += 100 // 100ms between key presses
            }
        }
        
        executeGestureSequence(gestures, 0, callback)
    }
    
    /**
     * Execute a sequence of gestures
     */
    private fun executeGestureSequence(
        gestures: List<GestureDescription>,
        index: Int,
        callback: (Boolean) -> Unit
    ) {
        if (index >= gestures.size) {
            callback(true)
            return
        }
        
        accessibilityService.dispatchGesture(
            gestures[index],
            object : AccessibilityService.GestureResultCallback() {
                override fun onCompleted(gestureDescription: GestureDescription?) {
                    // Execute next gesture
                    executeGestureSequence(gestures, index + 1, callback)
                }
                
                override fun onCancelled(gestureDescription: GestureDescription?) {
                    Log.w(TAG, "Gesture sequence cancelled at index $index")
                    callback(false)
                }
            },
            null
        )
    }
    
    /**
     * Get approximate key position on virtual keyboard
     * This is a simplified implementation - real implementation would need
     * keyboard layout detection and character mapping
     */
    private fun getKeyPosition(char: Char, keyboardBounds: Rect): Pair<Float, Float>? {
        // QWERTY layout approximation
        val qwertyRows = listOf(
            "qwertyuiop",
            "asdfghjkl",
            "zxcvbnm"
        )
        
        val keyWidth = keyboardBounds.width() / 10f
        val keyHeight = keyboardBounds.height() / 4f
        
        qwertyRows.forEachIndexed { rowIndex, row ->
            val charIndex = row.indexOf(char.lowercaseChar())
            if (charIndex != -1) {
                val x = keyboardBounds.left + (charIndex + 0.5f) * keyWidth
                val y = keyboardBounds.top + (rowIndex + 0.5f) * keyHeight
                return Pair(x, y)
            }
        }
        
        return null
    }
    
    /**
     * Set text directly using accessibility actions
     */
    private fun setTextDirectly(inputField: AccessibilityNodeInfo, text: String): Boolean {
        return try {
            val arguments = android.os.Bundle().apply {
                putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, text)
            }
            inputField.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)
        } catch (e: Exception) {
            Log.e(TAG, "Direct text setting failed", e)
            false
        }
    }
    
    /**
     * Detect virtual keyboard bounds
     */
    fun detectKeyboardBounds(): Rect? {
        val rootNode = accessibilityService.rootInActiveWindow ?: return null
        
        // Look for keyboard-related nodes
        val keyboardNodes = findKeyboardNodes(rootNode)
        
        if (keyboardNodes.isNotEmpty()) {
            val bounds = Rect()
            keyboardNodes.forEach { node ->
                val nodeBounds = Rect()
                node.getBoundsInScreen(nodeBounds)
                bounds.union(nodeBounds)
            }
            return bounds
        }
        
        return null
    }
    
    /**
     * Find nodes that likely belong to virtual keyboard
     */
    private fun findKeyboardNodes(root: AccessibilityNodeInfo): List<AccessibilityNodeInfo> {
        val keyboardNodes = mutableListOf<AccessibilityNodeInfo>()
        findKeyboardNodesRecursive(root, keyboardNodes)
        return keyboardNodes
    }
    
    private fun findKeyboardNodesRecursive(
        node: AccessibilityNodeInfo,
        result: MutableList<AccessibilityNodeInfo>
    ) {
        val className = node.className?.toString()
        val packageName = node.packageName?.toString()
        
        // Look for keyboard-related classes and packages
        if (className?.contains("keyboard", ignoreCase = true) == true ||
            className?.contains("inputmethod", ignoreCase = true) == true ||
            packageName?.contains("inputmethod", ignoreCase = true) == true ||
            packageName?.contains("keyboard", ignoreCase = true) == true) {
            result.add(node)
        }
        
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                findKeyboardNodesRecursive(child, result)
            }
        }
    }
    
    /**
     * Check if gesture-based injection is supported
     */
    fun isGestureInjectionSupported(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.N
    }
}
