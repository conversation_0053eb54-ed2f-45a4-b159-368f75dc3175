import '../../../Core/Utils/Extensions/context_extension.dart';
import '../../../Core/Utils/Extensions/localizations_extension.dart';
import '../../../Core/Utils/Widget/default_app_bar.dart';
import '../cubit/setting_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../Config/Cubit/app_cubit.dart';
import '../../../main.dart';
import '../../Home/View/Widgets/language_picker.dart';

class SettingPage extends StatelessWidget {
  const SettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<SettingCubit>();
    final local = context.local;
    return BlocBuilder<SettingCubit, SettingState>(
      builder: (context, state) {
        return Scaffold(
          appBar: DefaultAppBar(arowback: true),
          body: SingleChildScrollView(
            child: Align(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Column(
                  spacing: 10.h,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16.0),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.blue, width: 2.0),
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: Text(
                        local.description_setting_app,
                        style: TextStyle(fontSize: 14.0, color: Colors.black87),
                      ),
                    ),
                    _buildSwitchRow(
                      text: local.allow_the_app_to_translate_your_recordings,
                      value: cubit.state.allowTheAppToTranslateYourRecordings,
                      onChanged: (bool newValue) =>
                          cubit.allowTheAppToTranslateYourRecordings(),
                    ),

                    _buildSwitchRow(
                      text: local.allow_the_app_to_translate_texts,
                      value: cubit.state.allowTheAppToTranslateTexts,
                      onChanged: (bool newValue) =>
                          cubit.allowTheAppToTranslateTexts(),
                    ),
                    _buildSwitchRow(
                      text: local.symbol_appears_on_the_screen,
                      value: cubit.state.symbolAppearsOnTheScreen,
                      onChanged: (bool newValue) =>
                          cubit.symbolAppearsOnTheScreen(),
                    ),
                    _buildSwitchRow(
                      text: local.enable_pick_image,
                      value: cubit.state.enablePickImage,
                      onChanged: (bool newValue) => cubit.enablePickImage(),
                    ),
                    _buildSwitchRow(
                      text: local.enable_pick_file,
                      value: cubit.state.enablePickFile,
                      onChanged: (bool newValue) => cubit.enablePickFile(),
                    ),
                    _buildSwitchRow(
                      text: local.enable_camera,
                      value: cubit.state.enableCamera,
                      onChanged: (bool newValue) => cubit.enableCamera(),
                    ),
                    _buildSwitchRow(
                      text: local.enable_microphone,
                      value: cubit.state.enableMicrophone,
                      onChanged: (bool newValue) => cubit.enableMicrophone(),
                    ),

                    ListTile(
                      title: Text(
                        context
                            .watch<AppCubit>()
                            .languageModelNow()
                            .languageName,
                      ),
                      leading: context
                          .watch<AppCubit>()
                          .languageModelNow()
                          .flag,
                      onTap: () => context.buildCustomBottomSheet(
                        widgetBuilder: (context) => LanguagePicker(
                          languages: context.read<AppCubit>().state.locales,
                          onSelected: (language) {
                            final locale = Locale(language.languageCode);
                            context.read<AppCubit>().changeLanguage(locale);
                            kNavigationService.goBack();
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

Widget _buildSwitchRow({
  required String text,
  required bool value,
  required ValueChanged<bool> onChanged,
}) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 8.0),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            text,
            style: const TextStyle(fontSize: 16.0, color: Colors.black),
          ),
        ),
        10.horizontalSpace,
        Switch(
          value: value,
          onChanged: onChanged,
          activeTrackColor: Colors.blue.shade200,
          activeColor: Colors.blue.shade700,
          inactiveThumbColor: Colors.grey.shade400,
          inactiveTrackColor: Colors.grey.shade200,
        ),
      ],
    ),
  );
}
