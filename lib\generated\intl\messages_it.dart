// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a it locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'it';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts": MessageLookupByLibrary.simpleMessage(
            "Consenti a Il traduttore di tradurre automaticamente i testi e inviarli nella lingua selezionata usando l\'intelligenza artificiale."),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "Consenti a Il traduttore di tradurre automaticamente le tue registrazioni audio e inviarle nella lingua selezionata usando l\'intelligenza artificiale."),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "Consenti l\'accesso alle app del telefono."),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "Goditi un\'esperienza di traduzione istantanea e fluida con Il traduttore, dove puoi convertire facilmente e rapidamente testi, parole e registrazioni audio nella tua lingua preferita. Grazie alle tecnologie avanzate di intelligenza artificiale che garantiscono alta precisione e un\'esperienza confortevole."),
        "enable_camera": MessageLookupByLibrary.simpleMessage(
            "Consenti a Il traduttore di usare la fotocamera"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage(
            "Consenti a Il traduttore di usare il microfono"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "Consenti a Il traduttore di importare file dalla galleria"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "Consenti a Il traduttore di importare immagini dalla galleria"),
        "language": MessageLookupByLibrary.simpleMessage("Lingua"),
        "live_translation":
            MessageLookupByLibrary.simpleMessage("Scrivi la traduzione qui..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "Converti registrazioni audio in registrazione tradotta usando l\'intelligenza artificiale."),
        "name_app": MessageLookupByLibrary.simpleMessage("Il traduttore"),
        "search_language":
            MessageLookupByLibrary.simpleMessage("Cerca lingua..."),
        "symbol_appears_on_the_screen": MessageLookupByLibrary.simpleMessage(
            "Il simbolo appare sullo schermo"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "Traduci istantaneamente con Il traduttore, grazie al potere dell\'intelligenza artificiale."),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "Usa Il traduttore per tradurre testi ora.")
      };
}
