package com.example.al_tarjuman.injection

import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import com.example.al_tarjuman.utils.AppDetector
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger

/**
 * Coordinates multiple text injection strategies for maximum reliability
 */
class TextInjectionCoordinator(private val context: Context) {
    
    companion object {
        private const val TAG = "TextInjectionCoordinator"
        private const val INJECTION_TIMEOUT = 10000L
        private const val STRATEGY_TIMEOUT = 3000L
    }
    
    private val textInjectionManager = TextInjectionManager(context)
    private val gestureInjectionService: GestureInjectionService? = null // Will be initialized when AccessibilityService is available
    private val appDetector = AppDetector(context)
    private val mainHandler = Handler(Looper.getMainLooper())
    
    // Statistics and monitoring
    private val injectionAttempts = AtomicInteger(0)
    private val successfulInjections = AtomicInteger(0)
    private val strategyStats = ConcurrentHashMap<String, StrategyStats>()
    
    data class StrategyStats(
        var attempts: Int = 0,
        var successes: Int = 0,
        var totalDuration: Long = 0L,
        var lastUsed: Long = 0L
    ) {
        val successRate: Double get() = if (attempts > 0) successes.toDouble() / attempts else 0.0
        val averageDuration: Double get() = if (attempts > 0) totalDuration.toDouble() / attempts else 0.0
    }
    
    data class InjectionContext(
        val text: String,
        val targetPackage: String,
        val accessibilityService: AccessibilityService?,
        val inputField: AccessibilityNodeInfo?,
        val preferredStrategy: TextInjectionManager.InjectionStrategy? = null,
        val timeout: Long = INJECTION_TIMEOUT,
        val retryCount: Int = 0,
        val maxRetries: Int = 3
    )
    
    /**
     * Main injection method that coordinates all strategies
     */
    fun injectText(
        context: InjectionContext,
        callback: (Boolean, String?) -> Unit
    ) {
        val startTime = System.currentTimeMillis()
        injectionAttempts.incrementAndGet()
        
        Log.d(TAG, "Starting coordinated text injection for ${context.targetPackage}")
        
        // Validate context
        if (context.text.isBlank()) {
            callback(false, "Empty text provided")
            return
        }
        
        // Select optimal strategy based on app and context
        val strategies = selectOptimalStrategies(context)
        
        // Execute strategies in order of preference
        executeStrategiesSequentially(context, strategies, 0, startTime, callback)
    }
    
    /**
     * Select optimal injection strategies based on context
     */
    private fun selectOptimalStrategies(context: InjectionContext): List<InjectionStrategy> {
        val strategies = mutableListOf<InjectionStrategy>()
        
        // If a preferred strategy is specified, try it first
        context.preferredStrategy?.let { preferred ->
            strategies.add(InjectionStrategy(
                name = "Preferred_${preferred.name}",
                execute = { ctx, callback -> 
                    executeAccessibilityStrategy(ctx, preferred, callback)
                }
            ))
        }
        
        // App-specific strategy selection
        when (context.targetPackage) {
            "com.whatsapp", "com.whatsapp.w4b" -> {
                strategies.addAll(getWhatsAppStrategies())
            }
            "org.telegram.messenger" -> {
                strategies.addAll(getTelegramStrategies())
            }
            "com.facebook.orca" -> {
                strategies.addAll(getMessengerStrategies())
            }
            else -> {
                strategies.addAll(getGenericStrategies())
            }
        }
        
        // Add IME strategy if available
        if (IMEInjectionService.getInstance()?.canInjectText() == true) {
            strategies.add(InjectionStrategy(
                name = "IME_Direct",
                execute = { ctx, callback -> executeIMEStrategy(ctx, callback) }
            ))
        }
        
        // Add gesture strategy if supported
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && context.accessibilityService != null) {
            strategies.add(InjectionStrategy(
                name = "Gesture_Based",
                execute = { ctx, callback -> executeGestureStrategy(ctx, callback) }
            ))
        }
        
        return strategies
    }
    
    /**
     * Execute strategies sequentially until one succeeds
     */
    private fun executeStrategiesSequentially(
        context: InjectionContext,
        strategies: List<InjectionStrategy>,
        index: Int,
        startTime: Long,
        callback: (Boolean, String?) -> Unit
    ) {
        if (index >= strategies.size) {
            // All strategies failed
            callback(false, "All injection strategies failed")
            return
        }
        
        if (System.currentTimeMillis() - startTime > context.timeout) {
            callback(false, "Injection timeout exceeded")
            return
        }
        
        val strategy = strategies[index]
        val strategyStartTime = System.currentTimeMillis()
        
        Log.d(TAG, "Trying strategy: ${strategy.name}")
        
        // Update strategy stats
        val stats = strategyStats.getOrPut(strategy.name) { StrategyStats() }
        stats.attempts++
        stats.lastUsed = System.currentTimeMillis()
        
        // Execute strategy with timeout
        val timeoutHandler = Handler(Looper.getMainLooper())
        var completed = false
        
        val timeoutRunnable = Runnable {
            if (!completed) {
                completed = true
                Log.w(TAG, "Strategy ${strategy.name} timed out")
                // Try next strategy
                executeStrategiesSequentially(context, strategies, index + 1, startTime, callback)
            }
        }
        
        timeoutHandler.postDelayed(timeoutRunnable, STRATEGY_TIMEOUT)
        
        strategy.execute(context) { success, error ->
            timeoutHandler.removeCallbacks(timeoutRunnable)
            
            if (!completed) {
                completed = true
                val duration = System.currentTimeMillis() - strategyStartTime
                
                // Update stats
                stats.totalDuration += duration
                if (success) {
                    stats.successes++
                    successfulInjections.incrementAndGet()
                }
                
                if (success) {
                    Log.d(TAG, "Strategy ${strategy.name} succeeded in ${duration}ms")
                    callback(true, null)
                } else {
                    Log.d(TAG, "Strategy ${strategy.name} failed: $error")
                    // Try next strategy
                    executeStrategiesSequentially(context, strategies, index + 1, startTime, callback)
                }
            }
        }
    }
    
    /**
     * Execute accessibility-based strategy
     */
    private fun executeAccessibilityStrategy(
        context: InjectionContext,
        strategy: TextInjectionManager.InjectionStrategy,
        callback: (Boolean, String?) -> Unit
    ) {
        if (context.accessibilityService == null) {
            callback(false, "AccessibilityService not available")
            return
        }
        
        try {
            val result = textInjectionManager.injectText(
                context.accessibilityService,
                context.text,
                context.targetPackage,
                strategy
            )
            callback(result.success, result.error)
        } catch (e: Exception) {
            callback(false, "Accessibility injection failed: ${e.message}")
        }
    }
    
    /**
     * Execute IME-based strategy
     */
    private fun executeIMEStrategy(
        context: InjectionContext,
        callback: (Boolean, String?) -> Unit
    ) {
        val imeService = IMEInjectionService.getInstance()
        if (imeService == null || !imeService.canInjectText()) {
            callback(false, "IME service not available")
            return
        }
        
        try {
            // Clear existing text first
            imeService.clearExistingText()
            
            // Inject new text
            imeService.queueTextForInjection(context.text)
            
            // Give it a moment to process
            mainHandler.postDelayed({
                callback(true, null)
            }, 500)
        } catch (e: Exception) {
            callback(false, "IME injection failed: ${e.message}")
        }
    }
    
    /**
     * Execute gesture-based strategy
     */
    private fun executeGestureStrategy(
        context: InjectionContext,
        callback: (Boolean, String?) -> Unit
    ) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N || 
            context.accessibilityService == null || 
            context.inputField == null) {
            callback(false, "Gesture injection not supported")
            return
        }
        
        try {
            val gestureService = GestureInjectionService(context.accessibilityService)
            gestureService.injectTextWithGestures(context.inputField, context.text, callback)
        } catch (e: Exception) {
            callback(false, "Gesture injection failed: ${e.message}")
        }
    }
    
    // App-specific strategy configurations
    private fun getWhatsAppStrategies(): List<InjectionStrategy> {
        return listOf(
            InjectionStrategy("WhatsApp_SetText") { ctx, callback ->
                executeAccessibilityStrategy(ctx, TextInjectionManager.InjectionStrategy.SET_TEXT, callback)
            },
            InjectionStrategy("WhatsApp_Clipboard") { ctx, callback ->
                executeAccessibilityStrategy(ctx, TextInjectionManager.InjectionStrategy.CLIPBOARD_PASTE, callback)
            }
        )
    }
    
    private fun getTelegramStrategies(): List<InjectionStrategy> {
        return listOf(
            InjectionStrategy("Telegram_SetText") { ctx, callback ->
                executeAccessibilityStrategy(ctx, TextInjectionManager.InjectionStrategy.SET_TEXT, callback)
            },
            InjectionStrategy("Telegram_Hybrid") { ctx, callback ->
                executeAccessibilityStrategy(ctx, TextInjectionManager.InjectionStrategy.HYBRID, callback)
            }
        )
    }
    
    private fun getMessengerStrategies(): List<InjectionStrategy> {
        return listOf(
            InjectionStrategy("Messenger_Clipboard") { ctx, callback ->
                executeAccessibilityStrategy(ctx, TextInjectionManager.InjectionStrategy.CLIPBOARD_PASTE, callback)
            },
            InjectionStrategy("Messenger_SetText") { ctx, callback ->
                executeAccessibilityStrategy(ctx, TextInjectionManager.InjectionStrategy.SET_TEXT, callback)
            }
        )
    }
    
    private fun getGenericStrategies(): List<InjectionStrategy> {
        return listOf(
            InjectionStrategy("Generic_Smart") { ctx, callback ->
                executeAccessibilityStrategy(ctx, TextInjectionManager.InjectionStrategy.SMART, callback)
            },
            InjectionStrategy("Generic_Hybrid") { ctx, callback ->
                executeAccessibilityStrategy(ctx, TextInjectionManager.InjectionStrategy.HYBRID, callback)
            }
        )
    }
    
    /**
     * Get injection statistics
     */
    fun getStatistics(): Map<String, Any> {
        val totalAttempts = injectionAttempts.get()
        val totalSuccesses = successfulInjections.get()
        
        return mapOf(
            "totalAttempts" to totalAttempts,
            "totalSuccesses" to totalSuccesses,
            "overallSuccessRate" to if (totalAttempts > 0) totalSuccesses.toDouble() / totalAttempts else 0.0,
            "strategyStats" to strategyStats.mapValues { (_, stats) ->
                mapOf(
                    "attempts" to stats.attempts,
                    "successes" to stats.successes,
                    "successRate" to stats.successRate,
                    "averageDuration" to stats.averageDuration,
                    "lastUsed" to stats.lastUsed
                )
            }
        )
    }
    
    /**
     * Reset statistics
     */
    fun resetStatistics() {
        injectionAttempts.set(0)
        successfulInjections.set(0)
        strategyStats.clear()
    }
    
    /**
     * Get recommended strategy for an app
     */
    fun getRecommendedStrategy(packageName: String): TextInjectionManager.InjectionStrategy {
        val appStats = strategyStats.filter { it.key.contains(packageName, ignoreCase = true) }
        
        val bestStrategy = appStats.maxByOrNull { it.value.successRate }
        
        return when {
            bestStrategy?.key?.contains("SetText") == true -> TextInjectionManager.InjectionStrategy.SET_TEXT
            bestStrategy?.key?.contains("Clipboard") == true -> TextInjectionManager.InjectionStrategy.CLIPBOARD_PASTE
            bestStrategy?.key?.contains("Hybrid") == true -> TextInjectionManager.InjectionStrategy.HYBRID
            else -> TextInjectionManager.InjectionStrategy.SMART
        }
    }
    
    private data class InjectionStrategy(
        val name: String,
        val execute: (InjectionContext, (Boolean, String?) -> Unit) -> Unit
    )
}
