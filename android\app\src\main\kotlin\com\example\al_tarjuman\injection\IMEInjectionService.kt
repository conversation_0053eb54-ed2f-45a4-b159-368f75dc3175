package com.example.al_tarjuman.injection

import android.content.Context
import android.inputmethodservice.InputMethodService
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.KeyEvent
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputConnection
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * IME-based text injection service for reliable text input
 * This service can be used as a custom keyboard to inject text
 */
class IMEInjectionService : InputMethodService() {
    
    companion object {
        private const val TAG = "IMEInjectionService"
        private var instance: IMEInjectionService? = null
        
        fun getInstance(): IMEInjectionService? = instance
    }
    
    private val textQueue = ConcurrentLinkedQueue<String>()
    private val mainHandler = Handler(Looper.getMainLooper())
    private var currentInputConnection: InputConnection? = null
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        Log.d(TAG, "IME Injection Service created")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        instance = null
        Log.d(TAG, "IME Injection Service destroyed")
    }
    
    override fun onStartInput(attribute: EditorInfo?, restarting: Boolean) {
        super.onStartInput(attribute, restarting)
        currentInputConnection = currentInputConnection
        Log.d(TAG, "Input started for: ${attribute?.packageName}")
    }
    
    override fun onStartInputView(info: EditorInfo?, restarting: Boolean) {
        super.onStartInputView(info, restarting)
        
        // Process any queued text
        processQueuedText()
    }
    
    /**
     * Queue text for injection
     */
    fun queueTextForInjection(text: String) {
        textQueue.offer(text)
        Log.d(TAG, "Text queued for injection: ${text.take(50)}...")
        
        // Try to process immediately if we have an active input connection
        if (currentInputConnection != null) {
            mainHandler.post { processQueuedText() }
        }
    }
    
    /**
     * Process queued text for injection
     */
    private fun processQueuedText() {
        val inputConnection = currentInputConnection ?: return
        
        while (textQueue.isNotEmpty()) {
            val text = textQueue.poll()
            if (text != null) {
                injectTextViaIME(inputConnection, text)
            }
        }
    }
    
    /**
     * Inject text using IME input connection
     */
    private fun injectTextViaIME(inputConnection: InputConnection, text: String): Boolean {
        return try {
            // Method 1: Direct text commitment
            val success1 = inputConnection.commitText(text, 1)
            if (success1) {
                Log.d(TAG, "Text injected successfully via commitText")
                return true
            }
            
            // Method 2: Replace existing text
            val success2 = inputConnection.setComposingText(text, 1) && 
                          inputConnection.finishComposingText()
            if (success2) {
                Log.d(TAG, "Text injected successfully via composing text")
                return true
            }
            
            // Method 3: Select all and replace
            inputConnection.performContextMenuAction(android.R.id.selectAll)
            val success3 = inputConnection.commitText(text, 1)
            if (success3) {
                Log.d(TAG, "Text injected successfully via select all + commit")
                return true
            }
            
            false
        } catch (e: Exception) {
            Log.e(TAG, "IME text injection failed", e)
            false
        }
    }
    
    /**
     * Clear existing text in the input field
     */
    fun clearExistingText(): Boolean {
        val inputConnection = currentInputConnection ?: return false
        
        return try {
            // Get current text
            val currentText = inputConnection.getExtractedText(
                android.view.inputmethod.ExtractedTextRequest(), 0
            )?.text
            
            if (currentText != null && currentText.isNotEmpty()) {
                // Select all and delete
                inputConnection.performContextMenuAction(android.R.id.selectAll)
                inputConnection.deleteSurroundingText(0, currentText.length)
            }
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear existing text", e)
            false
        }
    }
    
    /**
     * Insert text at current cursor position
     */
    fun insertTextAtCursor(text: String): Boolean {
        val inputConnection = currentInputConnection ?: return false
        
        return try {
            inputConnection.commitText(text, 1)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to insert text at cursor", e)
            false
        }
    }
    
    /**
     * Replace selected text
     */
    fun replaceSelectedText(text: String): Boolean {
        val inputConnection = currentInputConnection ?: return false
        
        return try {
            inputConnection.commitText(text, 1)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to replace selected text", e)
            false
        }
    }
    
    /**
     * Simulate key press
     */
    fun simulateKeyPress(keyCode: Int): Boolean {
        val inputConnection = currentInputConnection ?: return false
        
        return try {
            inputConnection.sendKeyEvent(KeyEvent(KeyEvent.ACTION_DOWN, keyCode)) &&
            inputConnection.sendKeyEvent(KeyEvent(KeyEvent.ACTION_UP, keyCode))
        } catch (e: Exception) {
            Log.e(TAG, "Failed to simulate key press", e)
            false
        }
    }
    
    /**
     * Get current input field information
     */
    fun getCurrentInputInfo(): Map<String, Any?> {
        val inputConnection = currentInputConnection
        val editorInfo = currentInputEditorInfo
        
        return mapOf(
            "hasInputConnection" to (inputConnection != null),
            "packageName" to editorInfo?.packageName,
            "inputType" to editorInfo?.inputType,
            "imeOptions" to editorInfo?.imeOptions,
            "hint" to editorInfo?.hintText,
            "label" to editorInfo?.label
        )
    }
    
    /**
     * Check if IME is currently active
     */
    fun isIMEActive(): Boolean {
        return currentInputConnection != null && isInputViewShown
    }
    
    /**
     * Get current text from input field
     */
    fun getCurrentText(): String? {
        val inputConnection = currentInputConnection ?: return null
        
        return try {
            val extractedText = inputConnection.getExtractedText(
                android.view.inputmethod.ExtractedTextRequest(), 0
            )
            extractedText?.text?.toString()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get current text", e)
            null
        }
    }
    
    /**
     * Get text before cursor
     */
    fun getTextBeforeCursor(length: Int): String? {
        val inputConnection = currentInputConnection ?: return null
        
        return try {
            inputConnection.getTextBeforeCursor(length, 0)?.toString()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get text before cursor", e)
            null
        }
    }
    
    /**
     * Get text after cursor
     */
    fun getTextAfterCursor(length: Int): String? {
        val inputConnection = currentInputConnection ?: return null
        
        return try {
            inputConnection.getTextAfterCursor(length, 0)?.toString()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get text after cursor", e)
            null
        }
    }
    
    /**
     * Move cursor to position
     */
    fun moveCursor(position: Int): Boolean {
        val inputConnection = currentInputConnection ?: return false
        
        return try {
            inputConnection.setSelection(position, position)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to move cursor", e)
            false
        }
    }
    
    /**
     * Select text range
     */
    fun selectText(start: Int, end: Int): Boolean {
        val inputConnection = currentInputConnection ?: return false
        
        return try {
            inputConnection.setSelection(start, end)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to select text", e)
            false
        }
    }
    
    /**
     * Perform editor action (like send, done, etc.)
     */
    fun performEditorAction(actionCode: Int): Boolean {
        val inputConnection = currentInputConnection ?: return false
        
        return try {
            inputConnection.performEditorAction(actionCode)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to perform editor action", e)
            false
        }
    }
    
    /**
     * Check if text injection is possible
     */
    fun canInjectText(): Boolean {
        return currentInputConnection != null && 
               currentInputEditorInfo != null &&
               isInputViewShown
    }
    
    /**
     * Get injection capabilities
     */
    fun getInjectionCapabilities(): Map<String, Boolean> {
        val inputConnection = currentInputConnection
        val editorInfo = currentInputEditorInfo
        
        return mapOf(
            "canCommitText" to (inputConnection != null),
            "canSetComposingText" to (inputConnection != null),
            "canDeleteSurroundingText" to (inputConnection != null),
            "canSendKeyEvents" to (inputConnection != null),
            "canPerformEditorActions" to (inputConnection != null),
            "isPasswordField" to (editorInfo?.inputType?.and(
                android.text.InputType.TYPE_TEXT_VARIATION_PASSWORD) != 0),
            "isMultiline" to (editorInfo?.inputType?.and(
                android.text.InputType.TYPE_TEXT_FLAG_MULTI_LINE) != 0)
        )
    }
}
