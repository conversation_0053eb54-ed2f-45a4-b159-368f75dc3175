// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ja locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ja';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts":
            MessageLookupByLibrary.simpleMessage(
                "翻訳者が人工知能を使用してテキストを自動的に翻訳し、選択した言語で送信することを許可します。"),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "翻訳者が人工知能を使用して音声録音を自動的に翻訳し、選択した言語で送信することを許可します。"),
        "chat_now": MessageLookupByLibrary.simpleMessage("電話アプリへのアクセスを許可します。"),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "翻訳者で瞬時でスムーズな翻訳体験をお楽しみください。テキスト、単語、音声録音を簡単かつ迅速にお好みの言語に変換できます。高精度と快適な体験を保証する先進的な人工知能技術のおかげです。"),
        "enable_camera":
            MessageLookupByLibrary.simpleMessage("翻訳者がカメラを使用することを許可"),
        "enable_microphone":
            MessageLookupByLibrary.simpleMessage("翻訳者がマイクを使用することを許可"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "翻訳者がギャラリーからファイルをインポートすることを許可"),
        "enable_pick_image":
            MessageLookupByLibrary.simpleMessage("翻訳者がギャラリーから画像をインポートすることを許可"),
        "language": MessageLookupByLibrary.simpleMessage("言語"),
        "live_translation":
            MessageLookupByLibrary.simpleMessage("ここに翻訳を書いてください..."),
        "micro_now":
            MessageLookupByLibrary.simpleMessage("人工知能を使用して音声録音を翻訳録音に変換します。"),
        "name_app": MessageLookupByLibrary.simpleMessage("翻訳者"),
        "search_language": MessageLookupByLibrary.simpleMessage("言語を検索..."),
        "symbol_appears_on_the_screen":
            MessageLookupByLibrary.simpleMessage("シンボルが画面に表示されます"),
        "title_card":
            MessageLookupByLibrary.simpleMessage("人工知能の力により、翻訳者で瞬時に翻訳します。"),
        "translate_now":
            MessageLookupByLibrary.simpleMessage("翻訳者を使用して今すぐテキストを翻訳します。")
      };
}
