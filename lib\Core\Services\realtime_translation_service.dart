import 'dart:async';
import 'package:flutter/foundation.dart';
import 'platform_channel_service.dart';
import 'translator_model.dart';
import '../Storage/translation_storage.dart';

class RealtimeTranslationService {
  static RealtimeTranslationService? _instance;
  static RealtimeTranslationService get instance => _instance ??= RealtimeTranslationService._();
  
  RealtimeTranslationService._() {
    _initialize();
  }
  
  final PlatformChannelService _platformService = PlatformChannelService.instance;
  late StreamSubscription _messageSubscription;
  late StreamSubscription _accessibilitySubscription;
  late StreamSubscription _appFocusSubscription;
  
  // Configuration
  String _targetLanguage = 'en';
  String _sourceLanguage = 'auto';
  bool _isEnabled = false;
  bool _autoTranslateIncoming = true;
  bool _autoTranslateOutgoing = false;
  Set<String> _enabledApps = {};
  
  // State tracking
  final Map<String, Map<String, dynamic>> _activeMessages = {};
  String? _currentFocusedApp;
  
  // Streams for UI updates
  final StreamController<Map<String, dynamic>> _translationUpdateController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<bool> _serviceStatusController = 
      StreamController<bool>.broadcast();
  
  Stream<Map<String, dynamic>> get onTranslationUpdate => _translationUpdateController.stream;
  Stream<bool> get onServiceStatusChanged => _serviceStatusController.stream;
  
  void _initialize() {
    // Listen to messages from accessibility service
    _messageSubscription = _platformService.onMessageDetected.listen(_handleMessageDetected);
    
    // Listen to accessibility service status
    _accessibilitySubscription = _platformService.onAccessibilityStatusChanged.listen(_handleAccessibilityStatusChanged);
    
    // Listen to app focus changes
    _appFocusSubscription = _platformService.onAppFocusChanged.listen(_handleAppFocusChanged);
    
    // Load saved configuration
    _loadConfiguration();
  }
  
  Future<void> _loadConfiguration() async {
    try {
      final config = await TranslationStorage.getTranslationConfig();
      _targetLanguage = config['targetLanguage'] ?? 'en';
      _sourceLanguage = config['sourceLanguage'] ?? 'auto';
      _autoTranslateIncoming = config['autoTranslateIncoming'] ?? true;
      _autoTranslateOutgoing = config['autoTranslateOutgoing'] ?? false;
      _enabledApps = Set<String>.from(config['enabledApps'] ?? []);
    } catch (e) {
      debugPrint('Error loading translation configuration: $e');
    }
  }
  
  Future<void> _saveConfiguration() async {
    try {
      await TranslationStorage.saveTranslationConfig({
        'targetLanguage': _targetLanguage,
        'sourceLanguage': _sourceLanguage,
        'autoTranslateIncoming': _autoTranslateIncoming,
        'autoTranslateOutgoing': _autoTranslateOutgoing,
        'enabledApps': _enabledApps.toList(),
      });
    } catch (e) {
      debugPrint('Error saving translation configuration: $e');
    }
  }
  
  void _handleMessageDetected(Map<String, dynamic> data) async {
    final message = data['message'] as Map<String, dynamic>;
    final packageName = data['packageName'] as String;
    
    // Check if translation is enabled for this app
    if (!_isEnabled || !_enabledApps.contains(packageName)) {
      return;
    }
    
    final messageId = message['id'] as String;
    final text = message['text'] as String;
    final isIncoming = message['isIncoming'] as bool;
    
    // Check if we should translate this message
    if ((isIncoming && !_autoTranslateIncoming) || (!isIncoming && !_autoTranslateOutgoing)) {
      return;
    }
    
    // Store the message
    _activeMessages[messageId] = message;
    
    // Translate the message
    await _translateMessage(messageId, text, packageName);
  }
  
  Future<void> _translateMessage(String messageId, String text, String packageName) async {
    try {
      final translationResult = await TranslationService.translateChatMessage(
        text: text,
        targetLanguage: _targetLanguage,
        sourceLanguage: _sourceLanguage == 'auto' ? null : _sourceLanguage,
        shouldDetectLanguage: _sourceLanguage == 'auto',
      );
      
      if (translationResult['success'] == true) {
        // Update the message with translation
        final message = _activeMessages[messageId]!;
        message['translatedText'] = translationResult['translatedText'];
        message['isTranslated'] = true;
        message['sourceLanguage'] = translationResult['sourceLanguage'];
        
        // Show overlay
        await _platformService.showTranslationOverlay(message);
        
        // Save translation history
        await TranslationStorage.saveTranslationHistory({
          'messageId': messageId,
          'originalText': text,
          'translatedText': translationResult['translatedText'],
          'sourceLanguage': translationResult['sourceLanguage'],
          'targetLanguage': _targetLanguage,
          'appPackage': packageName,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        });
        
        // Notify UI
        _translationUpdateController.add({
          'type': 'translation_completed',
          'messageId': messageId,
          'message': message,
          'packageName': packageName,
        });
      } else {
        debugPrint('Translation failed: ${translationResult['error']}');
      }
    } catch (e) {
      debugPrint('Error translating message: $e');
    }
  }
  
  void _handleAccessibilityStatusChanged(bool isEnabled) {
    _serviceStatusController.add(isEnabled);
  }
  
  void _handleAppFocusChanged(Map<String, dynamic> data) {
    final packageName = data['packageName'] as String;
    final isFocused = data['isFocused'] as bool;
    
    if (isFocused) {
      _currentFocusedApp = packageName;
    } else if (_currentFocusedApp == packageName) {
      _currentFocusedApp = null;
    }
  }
  
  // Public methods for configuration
  Future<void> setTargetLanguage(String languageCode) async {
    _targetLanguage = languageCode;
    await _saveConfiguration();
  }
  
  Future<void> setSourceLanguage(String languageCode) async {
    _sourceLanguage = languageCode;
    await _saveConfiguration();
  }
  
  Future<void> setAutoTranslateIncoming(bool enabled) async {
    _autoTranslateIncoming = enabled;
    await _saveConfiguration();
  }
  
  Future<void> setAutoTranslateOutgoing(bool enabled) async {
    _autoTranslateOutgoing = enabled;
    await _saveConfiguration();
  }
  
  Future<void> setEnabledApps(Set<String> apps) async {
    _enabledApps = apps;
    await _saveConfiguration();
  }
  
  Future<void> enableService() async {
    _isEnabled = true;
    await _platformService.startOverlayService();
  }
  
  Future<void> disableService() async {
    _isEnabled = false;
    await _platformService.stopOverlayService();
  }
  
  // Text injection for outgoing messages
  Future<bool> translateAndInjectText(String text) async {
    if (!_isEnabled || _currentFocusedApp == null) {
      return false;
    }
    
    try {
      final translationResult = await TranslationService.translateChatMessage(
        text: text,
        targetLanguage: _targetLanguage,
        sourceLanguage: _sourceLanguage == 'auto' ? null : _sourceLanguage,
        shouldDetectLanguage: _sourceLanguage == 'auto',
      );
      
      if (translationResult['success'] == true) {
        final translatedText = translationResult['translatedText'] as String;
        return await _platformService.injectText(translatedText);
      }
    } catch (e) {
      debugPrint('Error translating and injecting text: $e');
    }
    
    return false;
  }
  
  // Getters
  String get targetLanguage => _targetLanguage;
  String get sourceLanguage => _sourceLanguage;
  bool get isEnabled => _isEnabled;
  bool get autoTranslateIncoming => _autoTranslateIncoming;
  bool get autoTranslateOutgoing => _autoTranslateOutgoing;
  Set<String> get enabledApps => _enabledApps;
  String? get currentFocusedApp => _currentFocusedApp;
  
  void dispose() {
    _messageSubscription.cancel();
    _accessibilitySubscription.cancel();
    _appFocusSubscription.cancel();
    _translationUpdateController.close();
    _serviceStatusController.close();
  }
}
