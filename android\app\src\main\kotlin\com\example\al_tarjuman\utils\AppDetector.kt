package com.example.al_tarjuman.utils

import android.content.Context
import android.content.pm.PackageManager
import android.util.Log

class AppDetector(private val context: Context) {
    
    companion object {
        private const val TAG = "AppDetector"
        
        // Supported chat applications with their package names and display names
        val SUPPORTED_APPS = mapOf(
            "com.whatsapp" to AppInfo("WhatsApp", "com.whatsapp"),
            "com.whatsapp.w4b" to <PERSON>ppIn<PERSON>("WhatsApp Business", "com.whatsapp.w4b"),
            "org.telegram.messenger" to AppInfo("Telegram", "org.telegram.messenger"),
            "com.facebook.orca" to AppInfo("Messenger", "com.facebook.orca"),
            "com.viber.voip" to AppInfo("Viber", "com.viber.voip"),
            "com.skype.raider" to AppInfo("Skype", "com.skype.raider"),
            "com.snapchat.android" to <PERSON><PERSON><PERSON>n<PERSON>("Snapchat", "com.snapchat.android"),
            "com.instagram.android" to AppInfo("Instagram", "com.instagram.android"),
            "com.twitter.android" to AppInfo("Twitter", "com.twitter.android"),
            "com.discord" to AppInfo("Discord", "com.discord"),
            "com.google.android.apps.messaging" to AppInfo("Messages", "com.google.android.apps.messaging"),
            "com.samsung.android.messaging" to AppInfo("Samsung Messages", "com.samsung.android.messaging"),
            "com.microsoft.teams" to AppInfo("Microsoft Teams", "com.microsoft.teams"),
            "us.zoom.videomeetings" to AppInfo("Zoom", "us.zoom.videomeetings"),
            "com.slack" to AppInfo("Slack", "com.slack")
        )
    }
    
    data class AppInfo(
        val displayName: String,
        val packageName: String,
        var isInstalled: Boolean = false,
        var versionName: String? = null,
        var versionCode: Long = 0
    )
    
    /**
     * Check if a specific app is installed
     */
    fun isAppInstalled(packageName: String): Boolean {
        return try {
            context.packageManager.getPackageInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }
    
    /**
     * Get all installed supported chat apps
     */
    fun getInstalledSupportedApps(): List<AppInfo> {
        val installedApps = mutableListOf<AppInfo>()
        
        SUPPORTED_APPS.values.forEach { appInfo ->
            if (isAppInstalled(appInfo.packageName)) {
                try {
                    val packageInfo = context.packageManager.getPackageInfo(appInfo.packageName, 0)
                    installedApps.add(
                        appInfo.copy(
                            isInstalled = true,
                            versionName = packageInfo.versionName,
                            versionCode = packageInfo.longVersionCode
                        )
                    )
                } catch (e: Exception) {
                    Log.w(TAG, "Error getting package info for ${appInfo.packageName}", e)
                    installedApps.add(appInfo.copy(isInstalled = true))
                }
            }
        }
        
        return installedApps.sortedBy { it.displayName }
    }
    
    /**
     * Check if a package name is a supported chat app
     */
    fun isSupportedChatApp(packageName: String): Boolean {
        return SUPPORTED_APPS.containsKey(packageName)
    }
    
    /**
     * Get app info for a specific package name
     */
    fun getAppInfo(packageName: String): AppInfo? {
        return SUPPORTED_APPS[packageName]?.let { appInfo ->
            if (isAppInstalled(packageName)) {
                try {
                    val packageInfo = context.packageManager.getPackageInfo(packageName, 0)
                    appInfo.copy(
                        isInstalled = true,
                        versionName = packageInfo.versionName,
                        versionCode = packageInfo.longVersionCode
                    )
                } catch (e: Exception) {
                    Log.w(TAG, "Error getting package info for $packageName", e)
                    appInfo.copy(isInstalled = true)
                }
            } else {
                appInfo.copy(isInstalled = false)
            }
        }
    }
    
    /**
     * Get the display name for a package name
     */
    fun getDisplayName(packageName: String): String {
        return SUPPORTED_APPS[packageName]?.displayName ?: packageName
    }
    
    /**
     * Check if the app supports specific features based on version
     */
    fun supportsAdvancedFeatures(packageName: String): Boolean {
        return when (packageName) {
            "com.whatsapp", "com.whatsapp.w4b" -> {
                // WhatsApp versions that support better accessibility
                try {
                    val packageInfo = context.packageManager.getPackageInfo(packageName, 0)
                    val versionName = packageInfo.versionName
                    // Check if version is 2.21.0 or higher (example)
                    versionName?.let { version ->
                        val parts = version.split(".")
                        if (parts.size >= 2) {
                            val major = parts[0].toIntOrNull() ?: 0
                            val minor = parts[1].toIntOrNull() ?: 0
                            major > 2 || (major == 2 && minor >= 21)
                        } else false
                    } ?: false
                } catch (e: Exception) {
                    false
                }
            }
            "org.telegram.messenger" -> {
                // Telegram generally has good accessibility support
                true
            }
            else -> {
                // Default to basic support for other apps
                false
            }
        }
    }
    
    /**
     * Get app-specific configuration for text extraction
     */
    fun getAppExtractionConfig(packageName: String): AppExtractionConfig {
        return when (packageName) {
            "com.whatsapp", "com.whatsapp.w4b" -> AppExtractionConfig(
                messageContainerClass = "android.widget.LinearLayout",
                messageTextClass = "android.widget.TextView",
                inputFieldClass = "android.widget.EditText",
                inputFieldHint = "Type a message",
                messageIndicators = listOf("android:id/message", "message_text"),
                timestampIndicators = listOf("time", "timestamp"),
                senderIndicators = listOf("sender", "contact_name")
            )
            "org.telegram.messenger" -> AppExtractionConfig(
                messageContainerClass = "android.widget.FrameLayout",
                messageTextClass = "android.widget.TextView",
                inputFieldClass = "android.widget.EditText",
                inputFieldHint = "Message",
                messageIndicators = listOf("org.telegram.messenger:id/chat_message_text"),
                timestampIndicators = listOf("time"),
                senderIndicators = listOf("name")
            )
            "com.facebook.orca" -> AppExtractionConfig(
                messageContainerClass = "android.widget.LinearLayout",
                messageTextClass = "android.widget.TextView",
                inputFieldClass = "android.widget.EditText",
                inputFieldHint = "Aa",
                messageIndicators = listOf("message_text", "text_content"),
                timestampIndicators = listOf("timestamp"),
                senderIndicators = listOf("sender_name")
            )
            else -> AppExtractionConfig(
                messageContainerClass = "android.widget.TextView",
                messageTextClass = "android.widget.TextView",
                inputFieldClass = "android.widget.EditText",
                inputFieldHint = "message",
                messageIndicators = listOf("message", "text"),
                timestampIndicators = listOf("time"),
                senderIndicators = listOf("sender")
            )
        }
    }
    
    data class AppExtractionConfig(
        val messageContainerClass: String,
        val messageTextClass: String,
        val inputFieldClass: String,
        val inputFieldHint: String,
        val messageIndicators: List<String>,
        val timestampIndicators: List<String>,
        val senderIndicators: List<String>
    )
}
