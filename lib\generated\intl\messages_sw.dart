// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a sw locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'sw';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts": MessageLookupByLibrary.simpleMessage(
            "Ruhusu Mfasiri kutafsiri kiotomatiki maandishi na kuyatuma kwa lugha iliyochaguliwa kwa kutumia akili bandia."),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "Ruhusu Mfasiri kutafsiri kiotomatiki rekodi zako za sauti na kuzituma kwa lugha iliyochaguliwa kwa kutumia akili bandia."),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "Ruhusu ufikiaji wa programu za simu."),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "Furahia uzoefu wa kutafsiri papo hapo na ulaini na Mfasiri, ambapo unaweza kwa urahisi na haraka kubadilisha maandishi, maneno na rekodi za sauti kuwa lugha unayopendelea. Kwa shukrani za teknolojia za hali ya juu za akili bandia ambazo zinahakikisha usahihi wa juu na uzoefu wa starehe."),
        "enable_camera": MessageLookupByLibrary.simpleMessage(
            "Ruhusu Mfasiri kutumia kamera"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage(
            "Ruhusu Mfasiri kutumia maikrofoni"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "Ruhusu Mfasiri kuleta faili kutoka kwenye galeri"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "Ruhusu Mfasiri kuleta picha kutoka kwenye galeri"),
        "language": MessageLookupByLibrary.simpleMessage("Lugha"),
        "live_translation":
            MessageLookupByLibrary.simpleMessage("Andika tafsiri hapa..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "Badilisha rekodi za sauti kuwa rekodi zilizotafsiriwa kwa kutumia akili bandia."),
        "name_app": MessageLookupByLibrary.simpleMessage("Mfasiri"),
        "search_language":
            MessageLookupByLibrary.simpleMessage("Tafuta lugha..."),
        "symbol_appears_on_the_screen": MessageLookupByLibrary.simpleMessage(
            "Alama inaonekana kwenye skrini"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "Tafsiri papo hapo na Mfasiri, kwa nguvu ya akili bandia."),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "Tumia Mfasiri kutafsiri maandishi sasa.")
      };
}
