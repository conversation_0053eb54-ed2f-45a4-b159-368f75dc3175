import 'language_picker.dart';

import '../../../../Config/Cubit/app_cubit.dart';
import '../../../../Core/Utils/Extensions/context_extension.dart';
import '../../../../main.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../Core/Resources/text_style.dart';
import '../../../../Core/Utils/Enum/type_cards.dart';
import '../../../../Core/Utils/Widget/build_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../Cubits/home_cubit/home_cubit.dart';

class BuildTrasnlateCard extends StatelessWidget {
  const BuildTrasnlateCard({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<HomeCubit>();
    return Container(
      height: TypeCards.cardtraslate.height.h,
      width: TypeCards.cardtraslate.width.sw,
      decoration: BoxDecoration(
        color: Color(TypeCards.cardtraslate.valueColors),
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: Color(TypeCards.cardtraslate.valueColors).withAlpha(50),
            spreadRadius: 0,
            blurRadius: 10,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          InkWell(
            onTap: () => context.buildCustomBottomSheet(
              widgetBuilder: (context) => LanguagePicker(
                languages: context.read<AppCubit>().state.locales,
                onSelected: (language) {
                  cubit.changeLanguage(true, language.languageName);
                  kNavigationService.goBack();
                },
              ),
            ),
            child: Text(
              cubit.state.languageFirstTranslate ?? "",
              style: AppTextStyles.h7Bold,
            ),
          ),
          InkWell(
            onTap: () => cubit.repeatedLanguage(),
            child: BuildImageAssets(svg: TypeCards.cardtraslate.image),
          ),
          InkWell(
            onTap: () => context.buildCustomBottomSheet(
              widgetBuilder: (context) => LanguagePicker(
                languages: context.read<AppCubit>().state.locales,
                onSelected: (language) {
                  cubit.changeLanguage(false, language.languageName);
                  kNavigationService.goBack();
                },
              ),
            ),
            child: Text(
              cubit.state.languageSecondTranslate ?? "",
              style: AppTextStyles.h7Bold,
            ),
          ),
        ],
      ),
    );
  }
}
