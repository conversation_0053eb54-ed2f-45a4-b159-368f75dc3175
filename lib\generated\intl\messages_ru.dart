// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ru locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ru';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts": MessageLookupByLibrary.simpleMessage(
            "Разрешите Переводчик автоматически переводить тексты и отправлять их на выбранном языке с помощью искусственного интеллекта."),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "Разрешите Переводчик автоматически переводить ваши аудиозаписи и отправлять их на выбранном языке с помощью искусственного интеллекта."),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "Разрешить доступ к приложениям телефона."),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "Наслаждайтесь мгновенным и плавным опытом перевода с Переводчик, где вы можете легко и быстро преобразовывать тексты, слова и аудиозаписи на ваш предпочитаемый язык. Благодаря передовым технологиям искусственного интеллекта, которые обеспечивают высокую точность и комфортный опыт."),
        "enable_camera": MessageLookupByLibrary.simpleMessage(
            "Разрешить Переводчик использовать камеру"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage(
            "Разрешить Переводчик использовать микрофон"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "Разрешить Переводчик импортировать файлы из галереи"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "Разрешить Переводчик импортировать изображения из галереи"),
        "language": MessageLookupByLibrary.simpleMessage("Язык"),
        "live_translation":
            MessageLookupByLibrary.simpleMessage("Напишите перевод здесь..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "Преобразуйте аудиозаписи в переведенную запись с помощью искусственного интеллекта."),
        "name_app": MessageLookupByLibrary.simpleMessage("Переводчик"),
        "search_language":
            MessageLookupByLibrary.simpleMessage("Поиск языка..."),
        "symbol_appears_on_the_screen":
            MessageLookupByLibrary.simpleMessage("Символ появляется на экране"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "Переводите мгновенно с Переводчик, благодаря силе искусственного интеллекта."),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "Используйте Переводчик для перевода текстов сейчас.")
      };
}
