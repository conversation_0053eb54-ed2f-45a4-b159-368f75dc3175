import 'dart:async';
import 'dart:ui';
import 'package:al_tarjuman/Core/Storage/Models/language_mode.dart';

import '../../Core/Storage/Local/local_storage_keys.dart';
import '../../Core/Storage/Local/local_storage_service.dart';
import 'app_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:icons_plus/icons_plus.dart';

import '../app_config.dart';

class AppCubit extends Cubit<AppState> with WidgetsBindingObserver {
  final Connectivity _connectivity = Connectivity();
  late StreamSubscription _subscription;

  AppCubit() : super(AppState(locale: Locale("ar"))) {
    WidgetsBinding.instance.addObserver(this);
    getAllLanguageName();
    _monitorConnection();
    _updateLanguage();
  }

  @override
  Future<void> close() {
    WidgetsBinding.instance.removeObserver(this);
    _subscription.cancel();
    return super.close();
  }

  @override
  void didChangePlatformBrightness() {
    _updateThemeMode();
  }

  Future<void> startAnimations() async {
    emit(state.copyWith(logoOpacity: 1.0));
  }

  Future<void> endAnimations() async {
    emit(state.copyWith(loadingOpacity: 1.0));
  }

  void getAllLanguageName() {
    final List<LanguageMode> languages = AppConfig.languages.entries.map((
      entry,
    ) {
      final languageCode = entry.key;
      final languageData = entry.value;
      final languageName = languageData['name'] as String;
      final flag = languageData['flag'] as Flag;
      return LanguageMode(languageCode, languageName, flag);
    }).toList();

    emit(state.copyWith(locales: languages));
  }

  LanguageMode languageModelNow() => state.locales.firstWhere(
    (element) => element.languageCode == state.locale.languageCode,
  );

  void _updateThemeMode() {}

  void _updateLanguage() async {
    final localeString = await LocalStorageService.getValue(
      LocalStorageKeys.selectedLanguage,
      defaultValue: false,
    );

    if (localeString != false) {
      emit(state.copyWith(locale: Locale(localeString)));
    } else {
      final deviceLocale = PlatformDispatcher.instance.locale.languageCode;
      emit(state.copyWith(locale: Locale(deviceLocale)));
    }
  }

  void toggleTheme() {
    // emit(
    //   state.copyWith(
    //     themeMode: state.themeMode == ThemeMode.light
    //         ? ThemeMode.dark
    //         : ThemeMode.light,
    //   ),
    // );
  }

  void changeLanguage(Locale locale) {
    emit(state.copyWith(locale: locale));
  }

  void _monitorConnection() {
    _subscription = _connectivity.onConnectivityChanged.listen((result) {
      if (result.isEmpty || result.contains(ConnectivityResult.none)) {
        emit(state.copyWith(internet: false));
      } else {
        emit(state.copyWith(internet: true));
      }
    });
  }

  bool getConnectivity() => state.internet;
}
