import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../Config/Cubit/app_cubit.dart';
import '../../../Config/app_config.dart';
import '../../../generated/l10n.dart';

extension AppLocalizationsExtension on BuildContext {
  S get local => S.of(this);

  bool get isRtl {
    final langCode = watch<AppCubit>().state.locale.languageCode;
    return ['ar', 'fa', 'ur', 'he'].contains(langCode);
  }

  List<String> get supportedLanguages => AppConfig.languageKeys;
}
