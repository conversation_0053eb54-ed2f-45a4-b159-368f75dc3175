package com.example.al_tarjuman

import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import com.example.al_tarjuman.channels.PlatformChannelManager

class MainActivity : FlutterActivity() {

    private val CHANNEL = "com.example.al_tarjuman/translator"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        val methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
        PlatformChannelManager.initialize(methodChannel, this)
    }
}
