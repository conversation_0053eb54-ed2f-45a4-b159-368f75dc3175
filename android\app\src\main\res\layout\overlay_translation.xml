<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/overlay_background"
    android:padding="12dp"
    android:maxWidth="280dp"
    android:elevation="8dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/headerText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Translation"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            android:textStyle="bold"
            android:layout_centerVertical="true" />

        <ImageView
            android:id="@+id/closeButton"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentEnd="true"
            android:src="@drawable/ic_close"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="2dp"
            android:contentDescription="Close translation" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#40FFFFFF"
        android:layout_marginVertical="8dp" />

    <TextView
        android:id="@+id/translationText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        android:lineSpacingExtra="2dp"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/originalText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#CCFFFFFF"
        android:textSize="11sp"
        android:textStyle="italic"
        android:maxLines="2"
        android:ellipsize="end" />

</LinearLayout>
