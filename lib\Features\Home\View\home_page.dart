import 'Cubits/home_cubit/home_cubit.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'Widgets/build_bigger_card.dart';
import 'Widgets/build_trasnlate_card.dart';
import 'Widgets/buildbody_cards.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../Core/Utils/Widget/default_app_bar.dart';
import 'package:flutter/material.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      builder: (context, state) {
        return Scaffold(
          appBar: DefaultAppBar(arowback: false),
          body: SingleChildScrollView(
            child: Align(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Column(
                  spacing: 30.h,
                  children: [
                    0.verticalSpace,
                    BuildBiggerCard(),
                    BuildbodyCards(),
                    BuildTrasnlateCard(),
                  ],
                ),
              ),
            ),
          ),
          resizeToAvoidBottomInset: true,
        );
      },
    );
  }
}
