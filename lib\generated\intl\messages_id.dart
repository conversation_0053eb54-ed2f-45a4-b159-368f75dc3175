// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a id locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'id';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts": MessageLookupByLibrary.simpleMessage(
            "Izinkan Penerjemah untuk secara otomatis menerjemahkan teks dan mengirimkannya dalam bahasa yang dipilih menggunakan kecerdasan buatan."),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "Izinkan Penerjemah untuk secara otomatis menerjemahkan rekaman audio Anda dan mengirimkannya dalam bahasa yang dipilih menggunakan kecerdasan buatan."),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "Izinkan akses ke aplikasi telepon."),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "Nikmati pengalaman terjemahan instan dan lancar dengan Penerjemah, di mana Anda dapat dengan mudah dan cepat mengkonversi teks, kata, dan rekaman audio ke bahasa pilihan Anda. Berkat teknologi kecerdasan buatan canggih yang menjamin akurasi tinggi dan pengalaman yang nyaman."),
        "enable_camera": MessageLookupByLibrary.simpleMessage(
            "Izinkan Penerjemah menggunakan kamera"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage(
            "Izinkan Penerjemah menggunakan mikrofon"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "Izinkan Penerjemah mengimpor file dari galeri"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "Izinkan Penerjemah mengimpor gambar dari galeri"),
        "language": MessageLookupByLibrary.simpleMessage("Bahasa"),
        "live_translation":
            MessageLookupByLibrary.simpleMessage("Tulis terjemahan di sini..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "Konversi rekaman audio menjadi rekaman terjemahan menggunakan kecerdasan buatan."),
        "name_app": MessageLookupByLibrary.simpleMessage("Penerjemah"),
        "search_language":
            MessageLookupByLibrary.simpleMessage("Cari bahasa..."),
        "symbol_appears_on_the_screen":
            MessageLookupByLibrary.simpleMessage("Simbol muncul di layar"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "Terjemahkan secara instan dengan Penerjemah, berkat kekuatan kecerdasan buatan."),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "Gunakan Penerjemah untuk menerjemahkan teks sekarang.")
      };
}
