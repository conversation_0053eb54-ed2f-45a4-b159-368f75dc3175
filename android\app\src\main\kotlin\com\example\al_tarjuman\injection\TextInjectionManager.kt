package com.example.al_tarjuman.injection

import android.accessibilityservice.AccessibilityService
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Rect
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import com.example.al_tarjuman.utils.AppDetector
import java.util.concurrent.ConcurrentHashMap

/**
 * Advanced text injection manager with multiple strategies and app-specific optimizations
 */
class TextInjectionManager(private val context: Context) {
    
    companion object {
        private const val TAG = "TextInjectionManager"
        private const val INJECTION_TIMEOUT = 5000L
        private const val RETRY_DELAY = 200L
        private const val MAX_RETRIES = 3
    }
    
    private val appDetector = AppDetector(context)
    private val mainHandler = Handler(Looper.getMainLooper())
    private val injectionHistory = ConcurrentHashMap<String, InjectionResult>()
    private val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    
    // Configuration
    private var preferredStrategy = InjectionStrategy.SMART
    private var enableRetries = true
    private var enableFallbacks = true
    private var typingSpeed = 50L // milliseconds between characters
    
    enum class InjectionStrategy {
        SMART,           // Automatically choose best strategy
        SET_TEXT,        // Direct text setting
        CLIPBOARD_PASTE, // Clipboard + paste
        SIMULATE_TYPING, // Character by character typing
        GESTURE_BASED,   // Touch gestures
        HYBRID          // Combination of strategies
    }
    
    data class InjectionRequest(
        val text: String,
        val targetPackage: String,
        val strategy: InjectionStrategy = InjectionStrategy.SMART,
        val retryCount: Int = 0,
        val timestamp: Long = System.currentTimeMillis(),
        val requestId: String = generateRequestId()
    ) {
        companion object {
            private fun generateRequestId(): String {
                return "inj_${System.currentTimeMillis()}_${(Math.random() * 1000).toInt()}"
            }
        }
    }
    
    data class InjectionResult(
        val requestId: String,
        val success: Boolean,
        val strategy: InjectionStrategy,
        val duration: Long,
        val error: String? = null,
        val retryCount: Int = 0,
        val timestamp: Long = System.currentTimeMillis()
    )
    
    /**
     * Main injection method with intelligent strategy selection
     */
    fun injectText(
        accessibilityService: AccessibilityService,
        text: String,
        targetPackage: String,
        strategy: InjectionStrategy = InjectionStrategy.SMART
    ): InjectionResult {
        val request = InjectionRequest(text, targetPackage, strategy)
        val startTime = System.currentTimeMillis()
        
        Log.d(TAG, "Starting text injection: ${request.requestId}")
        
        try {
            val inputField = findInputField(accessibilityService, targetPackage)
            if (inputField == null) {
                return InjectionResult(
                    requestId = request.requestId,
                    success = false,
                    strategy = strategy,
                    duration = System.currentTimeMillis() - startTime,
                    error = "No input field found"
                )
            }
            
            val selectedStrategy = if (strategy == InjectionStrategy.SMART) {
                selectOptimalStrategy(inputField, targetPackage, text)
            } else {
                strategy
            }
            
            val result = executeInjection(inputField, text, selectedStrategy, request)
            injectionHistory[request.requestId] = result
            
            Log.d(TAG, "Injection completed: ${result.success} in ${result.duration}ms using ${result.strategy}")
            return result
            
        } catch (e: Exception) {
            Log.e(TAG, "Injection failed", e)
            return InjectionResult(
                requestId = request.requestId,
                success = false,
                strategy = strategy,
                duration = System.currentTimeMillis() - startTime,
                error = e.message
            )
        }
    }
    
    private fun findInputField(
        accessibilityService: AccessibilityService,
        targetPackage: String
    ): AccessibilityNodeInfo? {
        val rootNode = accessibilityService.rootInActiveWindow ?: return null
        
        // Use app-specific configuration for better field detection
        val config = appDetector.getAppExtractionConfig(targetPackage)
        
        // Strategy 1: Look for EditText with specific hints
        val editTextNodes = findNodesByClassName(rootNode, "android.widget.EditText")
        
        // Try app-specific resource IDs first
        config.messageIndicators.forEach { indicator ->
            if (indicator.contains(":id/") && indicator.contains("input")) {
                val node = findNodeByResourceId(rootNode, indicator)
                if (node != null && node.isEditable && node.isFocusable) {
                    return node
                }
            }
        }
        
        // Strategy 2: Look for focused EditText
        val focusedField = editTextNodes.find { it.isFocused && it.isEditable }
        if (focusedField != null) return focusedField
        
        // Strategy 3: Look for EditText with message-related hints
        val hintBasedField = editTextNodes.find { node ->
            node.isEditable && node.isFocusable && hasMessageHint(node, config)
        }
        if (hintBasedField != null) return hintBasedField
        
        // Strategy 4: Look for EditText in bottom area (typical input location)
        val bottomField = editTextNodes.find { node ->
            if (node.isEditable && node.isFocusable) {
                val bounds = Rect()
                node.getBoundsInScreen(bounds)
                bounds.top > getScreenHeight() * 0.6 // Bottom 40% of screen
            } else false
        }
        if (bottomField != null) return bottomField
        
        // Strategy 5: Any editable and focusable EditText
        return editTextNodes.firstOrNull { it.isEditable && it.isFocusable }
    }
    
    private fun selectOptimalStrategy(
        inputField: AccessibilityNodeInfo,
        targetPackage: String,
        text: String
    ): InjectionStrategy {
        // App-specific strategy selection
        return when (targetPackage) {
            "com.whatsapp", "com.whatsapp.w4b" -> {
                // WhatsApp works well with SET_TEXT
                if (text.length < 100) InjectionStrategy.SET_TEXT else InjectionStrategy.CLIPBOARD_PASTE
            }
            "org.telegram.messenger" -> {
                // Telegram has good accessibility support
                InjectionStrategy.SET_TEXT
            }
            "com.facebook.orca" -> {
                // Messenger sometimes needs clipboard approach
                InjectionStrategy.CLIPBOARD_PASTE
            }
            else -> {
                // For unknown apps, try SET_TEXT first, fallback to clipboard
                if (text.length < 50) InjectionStrategy.SET_TEXT else InjectionStrategy.CLIPBOARD_PASTE
            }
        }
    }
    
    private fun executeInjection(
        inputField: AccessibilityNodeInfo,
        text: String,
        strategy: InjectionStrategy,
        request: InjectionRequest
    ): InjectionResult {
        val startTime = System.currentTimeMillis()
        
        val success = when (strategy) {
            InjectionStrategy.SET_TEXT -> injectUsingSetText(inputField, text)
            InjectionStrategy.CLIPBOARD_PASTE -> injectUsingClipboard(inputField, text)
            InjectionStrategy.SIMULATE_TYPING -> injectUsingTyping(inputField, text)
            InjectionStrategy.GESTURE_BASED -> injectUsingGestures(inputField, text)
            InjectionStrategy.HYBRID -> injectUsingHybrid(inputField, text)
            InjectionStrategy.SMART -> {
                // This shouldn't happen as SMART should be resolved earlier
                injectUsingSetText(inputField, text)
            }
        }
        
        val duration = System.currentTimeMillis() - startTime
        
        // If injection failed and retries are enabled, try fallback strategies
        if (!success && enableFallbacks && request.retryCount < MAX_RETRIES) {
            Log.d(TAG, "Primary strategy failed, trying fallback")
            return tryFallbackStrategy(inputField, text, strategy, request)
        }
        
        return InjectionResult(
            requestId = request.requestId,
            success = success,
            strategy = strategy,
            duration = duration,
            retryCount = request.retryCount
        )
    }
    
    private fun injectUsingSetText(inputField: AccessibilityNodeInfo, text: String): Boolean {
        return try {
            // Focus the field first
            inputField.performAction(AccessibilityNodeInfo.ACTION_FOCUS)
            Thread.sleep(100)
            
            // Set the text
            val arguments = Bundle().apply {
                putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, text)
            }
            inputField.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)
        } catch (e: Exception) {
            Log.e(TAG, "SET_TEXT injection failed", e)
            false
        }
    }
    
    private fun injectUsingClipboard(inputField: AccessibilityNodeInfo, text: String): Boolean {
        return try {
            // Save current clipboard content
            val originalClip = clipboardManager.primaryClip
            
            // Set our text to clipboard
            val clip = ClipData.newPlainText("translated_text", text)
            clipboardManager.setPrimaryClip(clip)
            
            // Focus the field
            inputField.performAction(AccessibilityNodeInfo.ACTION_FOCUS)
            Thread.sleep(100)
            
            // Clear existing text
            inputField.performAction(AccessibilityNodeInfo.ACTION_SELECT_ALL)
            Thread.sleep(50)
            
            // Paste
            val success = inputField.performAction(AccessibilityNodeInfo.ACTION_PASTE)
            
            // Restore original clipboard content
            originalClip?.let { clipboardManager.setPrimaryClip(it) }
            
            success
        } catch (e: Exception) {
            Log.e(TAG, "CLIPBOARD injection failed", e)
            false
        }
    }
    
    private fun injectUsingTyping(inputField: AccessibilityNodeInfo, text: String): Boolean {
        return try {
            // Focus and clear
            inputField.performAction(AccessibilityNodeInfo.ACTION_FOCUS)
            Thread.sleep(100)
            inputField.performAction(AccessibilityNodeInfo.ACTION_SELECT_ALL)
            Thread.sleep(50)
            
            // Type character by character (simplified - real implementation would need IME)
            // For now, we'll use SET_TEXT as a fallback
            val arguments = Bundle().apply {
                putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, text)
            }
            inputField.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)
        } catch (e: Exception) {
            Log.e(TAG, "TYPING injection failed", e)
            false
        }
    }
    
    private fun injectUsingGestures(inputField: AccessibilityNodeInfo, text: String): Boolean {
        // Gesture-based injection would require AccessibilityService.GestureDescription
        // For now, fallback to clipboard method
        return injectUsingClipboard(inputField, text)
    }
    
    private fun injectUsingHybrid(inputField: AccessibilityNodeInfo, text: String): Boolean {
        // Try SET_TEXT first, then clipboard if it fails
        return injectUsingSetText(inputField, text) || injectUsingClipboard(inputField, text)
    }
    
    private fun tryFallbackStrategy(
        inputField: AccessibilityNodeInfo,
        text: String,
        failedStrategy: InjectionStrategy,
        request: InjectionRequest
    ): InjectionResult {
        val fallbackStrategy = when (failedStrategy) {
            InjectionStrategy.SET_TEXT -> InjectionStrategy.CLIPBOARD_PASTE
            InjectionStrategy.CLIPBOARD_PASTE -> InjectionStrategy.SET_TEXT
            InjectionStrategy.SIMULATE_TYPING -> InjectionStrategy.CLIPBOARD_PASTE
            InjectionStrategy.GESTURE_BASED -> InjectionStrategy.SET_TEXT
            InjectionStrategy.HYBRID -> InjectionStrategy.CLIPBOARD_PASTE
            InjectionStrategy.SMART -> InjectionStrategy.SET_TEXT
        }
        
        val retryRequest = request.copy(
            strategy = fallbackStrategy,
            retryCount = request.retryCount + 1
        )
        
        // Add delay before retry
        Thread.sleep(RETRY_DELAY)
        
        return executeInjection(inputField, text, fallbackStrategy, retryRequest)
    }
    
    // Helper methods
    private fun findNodesByClassName(root: AccessibilityNodeInfo, className: String): List<AccessibilityNodeInfo> {
        val nodes = mutableListOf<AccessibilityNodeInfo>()
        findNodesByClassNameRecursive(root, className, nodes)
        return nodes
    }
    
    private fun findNodesByClassNameRecursive(
        node: AccessibilityNodeInfo,
        className: String,
        result: MutableList<AccessibilityNodeInfo>
    ) {
        if (node.className?.toString() == className) {
            result.add(node)
        }
        
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                findNodesByClassNameRecursive(child, className, result)
            }
        }
    }
    
    private fun findNodeByResourceId(root: AccessibilityNodeInfo, resourceId: String): AccessibilityNodeInfo? {
        if (root.viewIdResourceName == resourceId) {
            return root
        }
        
        for (i in 0 until root.childCount) {
            root.getChild(i)?.let { child ->
                val found = findNodeByResourceId(child, resourceId)
                if (found != null) return found
            }
        }
        
        return null
    }
    
    private fun hasMessageHint(node: AccessibilityNodeInfo, config: AppDetector.AppExtractionConfig): Boolean {
        val text = node.text?.toString()?.lowercase()
        val contentDesc = node.contentDescription?.toString()?.lowercase()
        
        val keywords = listOf(config.inputFieldHint.lowercase()) + 
                      listOf("message", "type", "write", "chat", "text", "send")
        
        return keywords.any { keyword ->
            text?.contains(keyword) == true || contentDesc?.contains(keyword) == true
        }
    }
    
    private fun getScreenHeight(): Int {
        return context.resources.displayMetrics.heightPixels
    }
    
    // Configuration methods
    fun setPreferredStrategy(strategy: InjectionStrategy) {
        preferredStrategy = strategy
    }
    
    fun setTypingSpeed(speedMs: Long) {
        typingSpeed = speedMs.coerceIn(10L, 500L)
    }
    
    fun setRetriesEnabled(enabled: Boolean) {
        enableRetries = enabled
    }
    
    fun setFallbacksEnabled(enabled: Boolean) {
        enableFallbacks = enabled
    }
    
    // Statistics and monitoring
    fun getInjectionHistory(): List<InjectionResult> {
        return injectionHistory.values.toList().sortedByDescending { it.timestamp }
    }
    
    fun getSuccessRate(): Double {
        val results = injectionHistory.values
        if (results.isEmpty()) return 0.0
        return results.count { it.success }.toDouble() / results.size
    }
    
    fun clearHistory() {
        injectionHistory.clear()
    }
}
