import 'package:hive/hive.dart';
import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';

import '../Models/social_app_model.dart';
import '../../Services/app_detection_service.dart';

class SocialAppsStorageService {
  static const String _socialAppsBoxKey = 'social_apps_preferences';
  static late Box<SocialAppModel> _socialAppsBox;

  static Future<void> init() async {
    try {
      _socialAppsBox = await Hive.openBox<SocialAppModel>(_socialAppsBoxKey);

      if (_socialAppsBox.isEmpty) {
        await _initializeInstalledApps();
      } else {
        await _updateStorageWithInstalledApps();
      }
    } catch (e) {
      rethrow;
    }
  }

  static List<SocialAppModel> getAllSocialApps() {
    try {
      return _socialAppsBox.values.toList()
        ..sort((a, b) => a.name.compareTo(b.name));
    } catch (e) {
      return [];
    }
  }


  static Future<bool> updateAppToggleState(String appId, bool isEnabled) async {
    try {
      final app = _socialAppsBox.get(appId);
      if (app != null) {
        final updatedApp = app.copyWith(
          isEnabled: isEnabled,
          lastUpdated: DateTime.now(),
        );
        await _socialAppsBox.put(appId, updatedApp);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }


  /// Get all enabled social apps
  static List<SocialAppModel> getEnabledApps() {
    try {
      return _socialAppsBox.values.where((app) => app.isEnabled).toList()
        ..sort((a, b) => a.name.compareTo(b.name));
    } catch (e) {
      return [];
    }
  }

  /// Get all disabled social apps
  static List<SocialAppModel> getDisabledApps() {
    try {
      return _socialAppsBox.values.where((app) => !app.isEnabled).toList()
        ..sort((a, b) => a.name.compareTo(b.name));
    } catch (e) {
      debugPrint('Error getting disabled apps: $e');
      return [];
    }
  }



  /// Check if a specific app is enabled
  static bool isAppEnabled(String appId) {
    try {
      final app = _socialAppsBox.get(appId);
      return app?.isEnabled ?? false;
    } catch (e) {
      return false;
    }
  }



  /// Initialize the box with only installed social apps
  /// All apps start with isEnabled = false
  static Future<void> _initializeInstalledApps() async {
    try {
      // Get list of installed social apps
      final installedAppNames =
          await AppDetectionService.getInstalledSocialApps();
      final defaultApps = _getDefaultSocialApps();

      // Only store apps that are actually installed
      for (final appData in defaultApps) {
        final appName = appData['name'] as String;
        if (installedAppNames.contains(appName)) {
          final app = SocialAppModel.fromMap(appData);
          await _socialAppsBox.put(app.id, app);
          debugPrint('Stored installed app: $appName');
        }
      }

    } catch (e) {
      await _initializeDefaultApps();
    }
  }

  /// Update existing storage to only keep installed apps
  /// Removes apps that are no longer installed
  static Future<void> _updateStorageWithInstalledApps() async {
    try {
      final installedAppNames =
          await AppDetectionService.getInstalledSocialApps();
      final currentApps = _socialAppsBox.values.toList();

      // Remove apps that are no longer installed
      for (final app in currentApps) {
        if (!installedAppNames.contains(app.name)) {
          await _socialAppsBox.delete(app.id);
          debugPrint('Removed uninstalled app: ${app.name}');
        }
      }

      // Add newly installed apps with default settings
      final defaultApps = _getDefaultSocialApps();
      final currentAppNames = _socialAppsBox.values
          .map((app) => app.name)
          .toSet();

      for (final appData in defaultApps) {
        final appName = appData['name'] as String;
        if (installedAppNames.contains(appName) &&
            !currentAppNames.contains(appName)) {
          final app = SocialAppModel.fromMap(appData);
          await _socialAppsBox.put(app.id, app);
          debugPrint('Added newly installed app: $appName');
        }
      }

      debugPrint('Updated storage: ${_socialAppsBox.length} installed apps');
    } catch (e) {
      debugPrint('Error updating storage with installed apps: $e');
    }
  }

  /// Initialize the box with default social apps data (fallback method)
  static Future<void> _initializeDefaultApps() async {
    final defaultApps = _getDefaultSocialApps();

    for (final appData in defaultApps) {
      final app = SocialAppModel.fromMap(appData);
      await _socialAppsBox.put(app.id, app);
    }
  }

  /// Get the default social apps configuration
  static List<Map<String, dynamic>> _getDefaultSocialApps() {
    return [
      {
        "value": false,
        'name': 'WhatsApp',
        'icon': Icon(Bootstrap.whatsapp, color: Color(0xFF25D366)),
      },
      {
        "value": false,
        'name': 'Messenger',
        'icon': Icon(Bootstrap.messenger, color: Color(0xFF0078FF)),
      },
      {
        "value": false,
        'name': 'Telegram',
        'icon': Icon(Bootstrap.telegram, color: Color(0xFF0088CC)),
      },
      {
        "value": false,
        'name': 'Instagram',
        'icon': Icon(Bootstrap.instagram, color: Color(0xFFE4405F)),
      },
      {
        "value": false,
        'name': 'Facebook',
        'icon': Icon(Bootstrap.facebook, color: Color(0xFF1877F2)),
      },
      {
        "value": false,
        'name': 'iMessage',
        'icon': Icon(Bootstrap.chat_left_text_fill, color: Color(0xFF32CD32)),
      },
      {
        "value": false,
        'name': 'Botim',
        'icon': Icon(Bootstrap.chat_dots, color: Color(0xFF00BAF2)),
      },
      {
        "value": false,
        'name': 'IMO',
        'icon': Icon(Bootstrap.chat, color: Color(0xFF00AEEF)),
      },
      {
        "value": false,
        'name': 'Snapchat',
        'icon': Icon(Bootstrap.snapchat, color: Color(0xFFFFFC00)),
      },
      {
        "value": false,
        'name': 'Viber',
        'icon': Icon(Bootstrap.phone_vibrate, color: Color(0xFF7360F2)),
      },
      {
        "value": false,
        'name': 'Google Chat',
        'icon': Icon(Bootstrap.google, color: Color(0xFF25AF5E)),
      },
      {
        "value": false,
        'name': 'LINE',
        'icon': Icon(Bootstrap.chat_left_text, color: Color(0xFF00C300)),
      },
    ];
  }

  /// Close the storage service and clean up resources
  static Future<void> dispose() async {
    try {
      await _socialAppsBox.close();
    } catch (e) {
      return; 
    }
  }
}
