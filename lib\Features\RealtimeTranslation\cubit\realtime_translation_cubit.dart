import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:meta/meta.dart';
import '../../../Core/Services/realtime_translation_service.dart';
import '../../../Core/Services/platform_channel_service.dart';

part 'realtime_translation_state.dart';

class RealtimeTranslationCubit extends Cubit<RealtimeTranslationState> {
  RealtimeTranslationCubit() : super(RealtimeTranslationInitial()) {
    _initialize();
  }
  
  final RealtimeTranslationService _translationService = RealtimeTranslationService.instance;
  final PlatformChannelService _platformService = PlatformChannelService.instance;
  
  late StreamSubscription _translationUpdateSubscription;
  late StreamSubscription _serviceStatusSubscription;
  
  void _initialize() {
    // Listen to translation updates
    _translationUpdateSubscription = _translationService.onTranslationUpdate.listen(
      (update) {
        if (state is RealtimeTranslationLoaded) {
          final currentState = state as RealtimeTranslationLoaded;
          emit(currentState.copyWith(
            lastTranslationUpdate: update,
            activeTranslations: {
              ...currentState.activeTranslations,
              update['messageId']: update['message'],
            },
          ));
        }
      },
    );
    
    // Listen to service status changes
    _serviceStatusSubscription = _translationService.onServiceStatusChanged.listen(
      (isEnabled) {
        if (state is RealtimeTranslationLoaded) {
          final currentState = state as RealtimeTranslationLoaded;
          emit(currentState.copyWith(isAccessibilityServiceEnabled: isEnabled));
        }
      },
    );
    
    // Load initial state
    loadConfiguration();
  }
  
  Future<void> loadConfiguration() async {
    emit(RealtimeTranslationLoading());
    
    try {
      // Check permissions
      final hasOverlayPermission = await _platformService.checkOverlayPermission();
      final hasAccessibilityPermission = await _platformService.checkAccessibilityPermission();
      
      // Get installed chat apps
      final installedApps = await _platformService.getInstalledChatApps();
      
      emit(RealtimeTranslationLoaded(
        isServiceEnabled: _translationService.isEnabled,
        hasOverlayPermission: hasOverlayPermission,
        isAccessibilityServiceEnabled: hasAccessibilityPermission,
        targetLanguage: _translationService.targetLanguage,
        sourceLanguage: _translationService.sourceLanguage,
        autoTranslateIncoming: _translationService.autoTranslateIncoming,
        autoTranslateOutgoing: _translationService.autoTranslateOutgoing,
        enabledApps: _translationService.enabledApps,
        installedChatApps: installedApps,
        currentFocusedApp: _translationService.currentFocusedApp,
        activeTranslations: {},
        lastTranslationUpdate: null,
      ));
    } catch (e) {
      emit(RealtimeTranslationError('Failed to load configuration: $e'));
    }
  }
  
  Future<void> requestOverlayPermission() async {
    await _platformService.requestOverlayPermission();
    // Check permission status after request
    await Future.delayed(const Duration(seconds: 1));
    await _checkPermissions();
  }
  
  Future<void> requestAccessibilityPermission() async {
    await _platformService.requestAccessibilityPermission();
  }
  
  Future<void> _checkPermissions() async {
    if (state is RealtimeTranslationLoaded) {
      final currentState = state as RealtimeTranslationLoaded;
      final hasOverlayPermission = await _platformService.checkOverlayPermission();
      final hasAccessibilityPermission = await _platformService.checkAccessibilityPermission();
      
      emit(currentState.copyWith(
        hasOverlayPermission: hasOverlayPermission,
        isAccessibilityServiceEnabled: hasAccessibilityPermission,
      ));
    }
  }
  
  Future<void> toggleService() async {
    if (state is RealtimeTranslationLoaded) {
      final currentState = state as RealtimeTranslationLoaded;
      
      if (currentState.isServiceEnabled) {
        await _translationService.disableService();
        emit(currentState.copyWith(isServiceEnabled: false));
      } else {
        // Check permissions before enabling
        if (!currentState.hasOverlayPermission) {
          emit(RealtimeTranslationError('Overlay permission is required'));
          return;
        }
        if (!currentState.isAccessibilityServiceEnabled) {
          emit(RealtimeTranslationError('Accessibility service must be enabled'));
          return;
        }
        
        await _translationService.enableService();
        emit(currentState.copyWith(isServiceEnabled: true));
      }
    }
  }
  
  Future<void> setTargetLanguage(String languageCode) async {
    await _translationService.setTargetLanguage(languageCode);
    if (state is RealtimeTranslationLoaded) {
      final currentState = state as RealtimeTranslationLoaded;
      emit(currentState.copyWith(targetLanguage: languageCode));
    }
  }
  
  Future<void> setSourceLanguage(String languageCode) async {
    await _translationService.setSourceLanguage(languageCode);
    if (state is RealtimeTranslationLoaded) {
      final currentState = state as RealtimeTranslationLoaded;
      emit(currentState.copyWith(sourceLanguage: languageCode));
    }
  }
  
  Future<void> setAutoTranslateIncoming(bool enabled) async {
    await _translationService.setAutoTranslateIncoming(enabled);
    if (state is RealtimeTranslationLoaded) {
      final currentState = state as RealtimeTranslationLoaded;
      emit(currentState.copyWith(autoTranslateIncoming: enabled));
    }
  }
  
  Future<void> setAutoTranslateOutgoing(bool enabled) async {
    await _translationService.setAutoTranslateOutgoing(enabled);
    if (state is RealtimeTranslationLoaded) {
      final currentState = state as RealtimeTranslationLoaded;
      emit(currentState.copyWith(autoTranslateOutgoing: enabled));
    }
  }
  
  Future<void> setEnabledApps(Set<String> apps) async {
    await _translationService.setEnabledApps(apps);
    if (state is RealtimeTranslationLoaded) {
      final currentState = state as RealtimeTranslationLoaded;
      emit(currentState.copyWith(enabledApps: apps));
    }
  }
  
  Future<bool> translateAndInjectText(String text) async {
    return await _translationService.translateAndInjectText(text);
  }
  
  void clearActiveTranslations() {
    if (state is RealtimeTranslationLoaded) {
      final currentState = state as RealtimeTranslationLoaded;
      emit(currentState.copyWith(activeTranslations: {}));
    }
  }
  
  @override
  Future<void> close() {
    _translationUpdateSubscription.cancel();
    _serviceStatusSubscription.cancel();
    return super.close();
  }
}
