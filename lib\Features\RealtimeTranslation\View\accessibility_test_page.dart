import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../Core/Services/platform_channel_service.dart';

class AccessibilityTestPage extends StatefulWidget {
  const AccessibilityTestPage({super.key});

  @override
  State<AccessibilityTestPage> createState() => _AccessibilityTestPageState();
}

class _AccessibilityTestPageState extends State<AccessibilityTestPage> {
  final PlatformChannelService _platformService = PlatformChannelService.instance;
  
  bool _isRunningTests = false;
  Map<String, dynamic>? _testResults;
  Map<String, dynamic>? _serviceStatus;
  String? _testReport;

  @override
  void initState() {
    super.initState();
    _loadServiceStatus();
  }

  Future<void> _loadServiceStatus() async {
    try {
      final status = await _platformService.getAccessibilityServiceStatus();
      setState(() {
        _serviceStatus = status;
      });
    } catch (e) {
      debugPrint('Error loading service status: $e');
    }
  }

  Future<void> _runTests() async {
    setState(() {
      _isRunningTests = true;
      _testResults = null;
      _testReport = null;
    });

    try {
      final results = await _platformService.runAccessibilityTests();
      setState(() {
        _testResults = results;
        _testReport = results['report'] as String?;
      });
    } catch (e) {
      setState(() {
        _testResults = {
          'success': false,
          'error': e.toString(),
        };
      });
    } finally {
      setState(() {
        _isRunningTests = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Accessibility Service Test'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadServiceStatus,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildServiceStatusCard(),
            SizedBox(height: 16.h),
            _buildTestControlCard(),
            SizedBox(height: 16.h),
            if (_testResults != null) _buildTestResultsCard(),
            if (_testReport != null) _buildTestReportCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceStatusCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Service Status',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 12.h),
            if (_serviceStatus != null) ...[
              ...(_serviceStatus!.entries.map((entry) => Padding(
                padding: EdgeInsets.symmetric(vertical: 2.h),
                child: Row(
                  children: [
                    SizedBox(
                      width: 120.w,
                      child: Text(
                        '${entry.key}:',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        entry.value.toString(),
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: _getStatusColor(entry.key, entry.value),
                        ),
                      ),
                    ),
                  ],
                ),
              ))),
            ] else ...[
              const CircularProgressIndicator(),
              SizedBox(height: 8.h),
              const Text('Loading service status...'),
            ],
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String key, dynamic value) {
    if (key == 'isEnabled' || key == 'isCurrentAppSupported') {
      return value == true ? Colors.green : Colors.red;
    }
    return Colors.black87;
  }

  Widget _buildTestControlCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Accessibility Tests',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 12.h),
            Text(
              'Run comprehensive tests to verify AccessibilityService functionality, app detection, and text extraction capabilities.',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 16.h),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRunningTests ? null : _runTests,
                icon: _isRunningTests
                    ? SizedBox(
                        width: 16.w,
                        height: 16.h,
                        child: const CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.play_arrow),
                label: Text(_isRunningTests ? 'Running Tests...' : 'Run Tests'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestResultsCard() {
    final results = _testResults!;
    final success = results['success'] as bool? ?? false;
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  success ? Icons.check_circle : Icons.error,
                  color: success ? Colors.green : Colors.red,
                  size: 24.r,
                ),
                SizedBox(width: 8.w),
                Text(
                  'Test Results',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            if (!success) ...[
              Text(
                'Error: ${results['error'] ?? 'Unknown error'}',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.red,
                ),
              ),
            ] else ...[
              if (results['results'] != null) ...[
                ...(results['results'] as List).map((result) => _buildTestResultItem(result)),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTestResultItem(Map<String, dynamic> result) {
    final testName = result['testName'] as String? ?? 'Unknown Test';
    final passed = result['passed'] as int? ?? 0;
    final total = result['total'] as int? ?? 0;
    final isSuccess = result['isSuccess'] as bool? ?? false;
    final successRate = result['successRate'] as double? ?? 0.0;

    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: isSuccess ? Colors.green.withOpacity(0.1) : Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: isSuccess ? Colors.green.withOpacity(0.3) : Colors.orange.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isSuccess ? Icons.check : Icons.warning,
                color: isSuccess ? Colors.green : Colors.orange,
                size: 16.r,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  testName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                '$passed/$total (${successRate.toStringAsFixed(1)}%)',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: isSuccess ? Colors.green : Colors.orange,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          if (result['details'] != null) ...[
            SizedBox(height: 8.h),
            ExpansionTile(
              title: const Text('Details'),
              children: [
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: (result['details'] as List).map((detail) => 
                      Padding(
                        padding: EdgeInsets.symmetric(vertical: 1.h),
                        child: Text(
                          detail.toString(),
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontFamily: 'monospace',
                          ),
                        ),
                      ),
                    ).toList(),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTestReportCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Detailed Report',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 12.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: SingleChildScrollView(
                child: Text(
                  _testReport!,
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontFamily: 'monospace',
                    height: 1.4,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
