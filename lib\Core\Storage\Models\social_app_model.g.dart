// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'social_app_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SocialAppModelAdapter extends TypeAdapter<SocialAppModel> {
  @override
  final int typeId = 0;

  @override
  SocialAppModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SocialAppModel(
      id: fields[0] as String,
      name: fields[1] as String,
      isEnabled: fields[2] as bool,
      iconName: fields[3] as String,
      iconColor: fields[4] as int,
      lastUpdated: fields[5] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, SocialAppModel obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.isEnabled)
      ..writeByte(3)
      ..write(obj.iconName)
      ..writeByte(4)
      ..write(obj.iconColor)
      ..writeByte(5)
      ..write(obj.lastUpdated);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SocialAppModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
