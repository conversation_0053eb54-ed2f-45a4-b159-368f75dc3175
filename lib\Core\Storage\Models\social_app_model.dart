import 'package:hive/hive.dart';
import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';

part 'social_app_model.g.dart';

@HiveType(typeId: 0)
class SocialAppModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  bool isEnabled;

  @HiveField(3)
  String iconName;

  @HiveField(4)
  int iconColor;

  @HiveField(5)
  DateTime lastUpdated;

  SocialAppModel({
    required this.id,
    required this.name,
    required this.isEnabled,
    required this.iconName,
    required this.iconColor,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  factory SocialAppModel.fromMap(Map<String, dynamic> map) {
    final Icon iconWidget = map['icon'] as Icon;

    return SocialAppModel(
      id: _generateIdFromName(map['name'] as String),
      name: map['name'] as String,
      isEnabled: map['value'] as bool,
      iconName: _getIconNameFromWidget(iconWidget),
      iconColor: _getColorValue(iconWidget.color),
    );
  }

  Map<String, dynamic> toMap() {
    return {'name': name, 'value': isEnabled, 'icon': _buildIconFromData()};
  }

  /// Create a copy of this model with updated values
  SocialAppModel copyWith({
    String? id,
    String? name,
    bool? isEnabled,
    String? iconName,
    int? iconColor,
    DateTime? lastUpdated,
  }) {
    return SocialAppModel(
      id: id ?? this.id,
      name: name ?? this.name,
      isEnabled: isEnabled ?? this.isEnabled,
      iconName: iconName ?? this.iconName,
      iconColor: iconColor ?? this.iconColor,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  /// Build Flutter Icon widget from stored data
  Icon _buildIconFromData() {
    return Icon(_getIconDataFromName(iconName), color: Color(iconColor));
  }

  /// Generate a unique ID from the app name
  static String _generateIdFromName(String name) {
    return name.toLowerCase().replaceAll(' ', '_');
  }

  /// Get color value in a way that avoids deprecated warnings
  static int _getColorValue(Color? color) {
    final targetColor = color ?? Colors.grey;
    // Using the recommended approach instead of deprecated .value
    return (targetColor.a * 255).round() << 24 |
        (targetColor.r * 255).round() << 16 |
        (targetColor.g * 255).round() << 8 |
        (targetColor.b * 255).round();
  }

  /// Extract icon name from Icon widget (simplified mapping)
  static String _getIconNameFromWidget(Icon iconWidget) {
    // This is a simplified approach - in a real implementation,
    // you might want to create a more comprehensive mapping
    final iconData = iconWidget.icon;

    if (iconData == Bootstrap.whatsapp) return 'whatsapp';
    if (iconData == Bootstrap.messenger) return 'messenger';
    if (iconData == Bootstrap.telegram) return 'telegram';
    if (iconData == Bootstrap.instagram) return 'instagram';
    if (iconData == Bootstrap.facebook) return 'facebook';
    if (iconData == Bootstrap.chat_left_text_fill) return 'imessage';
    if (iconData == Bootstrap.chat_dots) return 'botim';
    if (iconData == Bootstrap.chat) return 'imo';
    if (iconData == Bootstrap.snapchat) return 'snapchat';
    if (iconData == Bootstrap.phone_vibrate) return 'viber';
    if (iconData == Bootstrap.google) return 'google_chat';
    if (iconData == Bootstrap.chat_left_text) return 'line';

    return 'unknown';
  }

  /// Get IconData from stored icon name
  static IconData _getIconDataFromName(String iconName) {
    switch (iconName) {
      case 'whatsapp':
        return Bootstrap.whatsapp;
      case 'messenger':
        return Bootstrap.messenger;
      case 'telegram':
        return Bootstrap.telegram;
      case 'instagram':
        return Bootstrap.instagram;
      case 'facebook':
        return Bootstrap.facebook;
      case 'imessage':
        return Bootstrap.chat_left_text_fill;
      case 'botim':
        return Bootstrap.chat_dots;
      case 'imo':
        return Bootstrap.chat;
      case 'snapchat':
        return Bootstrap.snapchat;
      case 'viber':
        return Bootstrap.phone_vibrate;
      case 'google_chat':
        return Bootstrap.google;
      case 'line':
        return Bootstrap.chat_left_text;
      default:
        return Bootstrap.chat;
    }
  }

  /// Get the Icon widget for UI display
  Icon get iconWidget => _buildIconFromData();

  @override
  String toString() {
    return 'SocialAppModel(id: $id, name: $name, isEnabled: $isEnabled, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SocialAppModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
