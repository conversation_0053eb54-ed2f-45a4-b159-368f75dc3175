import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../cubit/realtime_translation_cubit.dart';

class TranslationSettingsPage extends StatelessWidget {
  const TranslationSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Translation Settings'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: BlocBuilder<RealtimeTranslationCubit, RealtimeTranslationState>(
        builder: (context, state) {
          if (state is RealtimeTranslationLoaded) {
            return _buildSettingsContent(context, state);
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildSettingsContent(BuildContext context, RealtimeTranslationLoaded state) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildLanguageSettings(context, state),
          SizedBox(height: 16.h),
          _buildAppSettings(context, state),
          SizedBox(height: 16.h),
          _buildTranslationSettings(context, state),
        ],
      ),
    );
  }

  Widget _buildLanguageSettings(BuildContext context, RealtimeTranslationLoaded state) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Language Settings',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 16.h),
            ListTile(
              title: const Text('Target Language'),
              subtitle: Text(_getLanguageName(state.targetLanguage)),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showLanguageSelector(context, 'target', state.targetLanguage),
            ),
            ListTile(
              title: const Text('Source Language'),
              subtitle: Text(_getLanguageName(state.sourceLanguage)),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showLanguageSelector(context, 'source', state.sourceLanguage),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppSettings(BuildContext context, RealtimeTranslationLoaded state) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Supported Apps',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 16.h),
            ...state.installedChatApps.map((app) => SwitchListTile(
              title: Text(app['name'] ?? ''),
              subtitle: Text(app['package'] ?? ''),
              value: state.enabledApps.contains(app['package']),
              onChanged: (enabled) {
                final newEnabledApps = Set<String>.from(state.enabledApps);
                if (enabled) {
                  newEnabledApps.add(app['package']!);
                } else {
                  newEnabledApps.remove(app['package']);
                }
                context.read<RealtimeTranslationCubit>().setEnabledApps(newEnabledApps);
              },
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildTranslationSettings(BuildContext context, RealtimeTranslationLoaded state) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Translation Behavior',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 16.h),
            SwitchListTile(
              title: const Text('Auto Translate Incoming Messages'),
              subtitle: const Text('Automatically translate messages you receive'),
              value: state.autoTranslateIncoming,
              onChanged: (value) => context.read<RealtimeTranslationCubit>().setAutoTranslateIncoming(value),
            ),
            SwitchListTile(
              title: const Text('Auto Translate Outgoing Messages'),
              subtitle: const Text('Automatically translate messages you send'),
              value: state.autoTranslateOutgoing,
              onChanged: (value) => context.read<RealtimeTranslationCubit>().setAutoTranslateOutgoing(value),
            ),
          ],
        ),
      ),
    );
  }

  void _showLanguageSelector(BuildContext context, String type, String currentLanguage) {
    final languages = {
      'auto': 'Auto Detect',
      'en': 'English',
      'ar': 'Arabic',
      'es': 'Spanish',
      'fr': 'French',
      'de': 'German',
      'it': 'Italian',
      'pt': 'Portuguese',
      'ru': 'Russian',
      'ja': 'Japanese',
      'ko': 'Korean',
      'zh': 'Chinese',
    };

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Select ${type == 'target' ? 'Target' : 'Source'} Language'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: languages.entries.map((entry) => ListTile(
              title: Text(entry.value),
              trailing: currentLanguage == entry.key ? const Icon(Icons.check) : null,
              onTap: () {
                Navigator.of(context).pop();
                if (type == 'target') {
                  context.read<RealtimeTranslationCubit>().setTargetLanguage(entry.key);
                } else {
                  context.read<RealtimeTranslationCubit>().setSourceLanguage(entry.key);
                }
              },
            )).toList(),
          ),
        ),
      ),
    );
  }

  String _getLanguageName(String code) {
    final languages = {
      'auto': 'Auto Detect',
      'en': 'English',
      'ar': 'Arabic',
      'es': 'Spanish',
      'fr': 'French',
      'de': 'German',
      'it': 'Italian',
      'pt': 'Portuguese',
      'ru': 'Russian',
      'ja': 'Japanese',
      'ko': 'Korean',
      'zh': 'Chinese',
    };
    return languages[code] ?? code.toUpperCase();
  }
}
