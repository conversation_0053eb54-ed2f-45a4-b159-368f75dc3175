import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';

class TranslationStorage {
  static const String _configBoxName = 'translation_config';
  static const String _historyBoxName = 'translation_history';
  static const String _cacheBoxName = 'translation_cache';
  
  static Box<Map>? _configBox;
  static Box<Map>? _historyBox;
  static Box<Map>? _cacheBox;
  
  static Future<void> initialize() async {
    await Hive.initFlutter();
    
    _configBox = await Hive.openBox<Map>(_configBoxName);
    _historyBox = await Hive.openBox<Map>(_historyBoxName);
    _cacheBox = await Hive.openBox<Map>(_cacheBoxName);
  }
  
  // Configuration methods
  static Future<Map<String, dynamic>> getTranslationConfig() async {
    await _ensureInitialized();
    final config = _configBox!.get('config');
    return Map<String, dynamic>.from(config ?? {});
  }
  
  static Future<void> saveTranslationConfig(Map<String, dynamic> config) async {
    await _ensureInitialized();
    await _configBox!.put('config', config);
  }
  
  // Translation history methods
  static Future<void> saveTranslationHistory(Map<String, dynamic> translation) async {
    await _ensureInitialized();
    final timestamp = translation['timestamp'] ?? DateTime.now().millisecondsSinceEpoch;
    await _historyBox!.put(timestamp.toString(), translation);
  }
  
  static Future<List<Map<String, dynamic>>> getTranslationHistory({
    int limit = 100,
    String? appPackage,
  }) async {
    await _ensureInitialized();
    
    final allHistory = _historyBox!.values
        .map((e) => Map<String, dynamic>.from(e))
        .toList();
    
    // Filter by app package if specified
    if (appPackage != null) {
      allHistory.removeWhere((item) => item['appPackage'] != appPackage);
    }
    
    // Sort by timestamp (newest first)
    allHistory.sort((a, b) => (b['timestamp'] ?? 0).compareTo(a['timestamp'] ?? 0));
    
    // Apply limit
    return allHistory.take(limit).toList();
  }
  
  static Future<void> clearTranslationHistory() async {
    await _ensureInitialized();
    await _historyBox!.clear();
  }
  
  // Translation cache methods
  static Future<String?> getCachedTranslation(String cacheKey) async {
    await _ensureInitialized();
    final cached = _cacheBox!.get(cacheKey);
    if (cached != null) {
      final cacheData = Map<String, dynamic>.from(cached);
      final expiry = cacheData['expiry'] as int?;
      
      // Check if cache is still valid (24 hours)
      if (expiry != null && DateTime.now().millisecondsSinceEpoch < expiry) {
        return cacheData['translation'] as String?;
      } else {
        // Remove expired cache
        await _cacheBox!.delete(cacheKey);
      }
    }
    return null;
  }
  
  static Future<void> setCachedTranslation(String cacheKey, String translation) async {
    await _ensureInitialized();
    final expiry = DateTime.now().add(const Duration(hours: 24)).millisecondsSinceEpoch;
    await _cacheBox!.put(cacheKey, {
      'translation': translation,
      'expiry': expiry,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }
  
  static Future<void> clearTranslationCache() async {
    await _ensureInitialized();
    await _cacheBox!.clear();
  }
  
  // App-specific settings
  static Future<Map<String, dynamic>> getAppSettings(String packageName) async {
    await _ensureInitialized();
    final settings = _configBox!.get('app_$packageName');
    return Map<String, dynamic>.from(settings ?? {});
  }
  
  static Future<void> saveAppSettings(String packageName, Map<String, dynamic> settings) async {
    await _ensureInitialized();
    await _configBox!.put('app_$packageName', settings);
  }
  
  // Statistics
  static Future<Map<String, dynamic>> getTranslationStats() async {
    await _ensureInitialized();
    
    final allHistory = _historyBox!.values
        .map((e) => Map<String, dynamic>.from(e))
        .toList();
    
    final stats = <String, dynamic>{
      'totalTranslations': allHistory.length,
      'appsUsed': <String>{},
      'languagePairs': <String, int>{},
      'dailyUsage': <String, int>{},
    };
    
    for (final translation in allHistory) {
      // Count apps
      final appPackage = translation['appPackage'] as String?;
      if (appPackage != null) {
        (stats['appsUsed'] as Set<String>).add(appPackage);
      }
      
      // Count language pairs
      final sourceLang = translation['sourceLanguage'] as String?;
      final targetLang = translation['targetLanguage'] as String?;
      if (sourceLang != null && targetLang != null) {
        final pair = '$sourceLang-$targetLang';
        final languagePairs = stats['languagePairs'] as Map<String, int>;
        languagePairs[pair] = (languagePairs[pair] ?? 0) + 1;
      }
      
      // Count daily usage
      final timestamp = translation['timestamp'] as int?;
      if (timestamp != null) {
        final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
        final dateKey = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
        final dailyUsage = stats['dailyUsage'] as Map<String, int>;
        dailyUsage[dateKey] = (dailyUsage[dateKey] ?? 0) + 1;
      }
    }
    
    // Convert Set to List for serialization
    stats['appsUsed'] = (stats['appsUsed'] as Set<String>).toList();
    
    return stats;
  }
  
  // Maintenance
  static Future<void> cleanupOldData() async {
    await _ensureInitialized();
    
    // Remove history older than 30 days
    final cutoffTime = DateTime.now().subtract(const Duration(days: 30)).millisecondsSinceEpoch;
    final keysToRemove = <String>[];
    
    for (final key in _historyBox!.keys) {
      final timestamp = int.tryParse(key.toString()) ?? 0;
      if (timestamp < cutoffTime) {
        keysToRemove.add(key.toString());
      }
    }
    
    for (final key in keysToRemove) {
      await _historyBox!.delete(key);
    }
    
    // Remove expired cache entries
    final cacheKeysToRemove = <String>[];
    final now = DateTime.now().millisecondsSinceEpoch;
    
    for (final key in _cacheBox!.keys) {
      final cached = _cacheBox!.get(key);
      if (cached != null) {
        final cacheData = Map<String, dynamic>.from(cached);
        final expiry = cacheData['expiry'] as int?;
        if (expiry != null && now > expiry) {
          cacheKeysToRemove.add(key.toString());
        }
      }
    }
    
    for (final key in cacheKeysToRemove) {
      await _cacheBox!.delete(key);
    }
  }
  
  static Future<void> _ensureInitialized() async {
    if (_configBox == null || _historyBox == null || _cacheBox == null) {
      await initialize();
    }
  }
  
  static Future<void> dispose() async {
    await _configBox?.close();
    await _historyBox?.close();
    await _cacheBox?.close();
  }
}
