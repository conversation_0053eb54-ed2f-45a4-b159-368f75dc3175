import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../cubit/realtime_translation_cubit.dart';
import 'permissions_setup_page.dart';
import 'translation_settings_page.dart';

class RealtimeTranslationPage extends StatelessWidget {
  const RealtimeTranslationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => RealtimeTranslationCubit(),
      child: const _RealtimeTranslationView(),
    );
  }
}

class _RealtimeTranslationView extends StatelessWidget {
  const _RealtimeTranslationView();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Real-time Translation'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _navigateToSettings(context),
          ),
        ],
      ),
      body: BlocConsumer<RealtimeTranslationCubit, RealtimeTranslationState>(
        listener: (context, state) {
          if (state is RealtimeTranslationError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is RealtimeTranslationLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is RealtimeTranslationLoaded) {
            return _buildMainContent(context, state);
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildMainContent(
    BuildContext context,
    RealtimeTranslationLoaded state,
  ) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildServiceStatusCard(context, state),
          SizedBox(height: 16.h),
          if (!state.hasRequiredPermissions) ...[
            _buildPermissionsWarning(context, state),
            SizedBox(height: 16.h),
          ],
          _buildQuickSettings(context, state),
          SizedBox(height: 16.h),
          _buildEnabledAppsCard(context, state),
          SizedBox(height: 16.h),
          _buildActiveTranslationsCard(context, state),
        ],
      ),
    );
  }

  Widget _buildServiceStatusCard(
    BuildContext context,
    RealtimeTranslationLoaded state,
  ) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: state.isServiceEnabled
                        ? Colors.green.withOpacity(0.1)
                        : Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    state.isServiceEnabled
                        ? Icons.translate
                        : Icons.translate_outlined,
                    color: state.isServiceEnabled ? Colors.green : Colors.grey,
                    size: 32.r,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Translation Service',
                        style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        state.isServiceEnabled
                            ? S.of(context).service_active ?? 'Active'
                            : S.of(context).service_inactive ?? 'Inactive',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: state.isServiceEnabled
                              ? Colors.green
                              : Colors.grey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: state.canEnableService
                    ? () => context
                          .read<RealtimeTranslationCubit>()
                          .toggleService()
                    : () => _navigateToPermissions(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: state.isServiceEnabled
                      ? Colors.red
                      : Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                child: Text(
                  state.canEnableService
                      ? (state.isServiceEnabled
                            ? S.of(context).stop_service ?? 'Stop Service'
                            : S.of(context).start_service ?? 'Start Service')
                      : S.of(context).setup_permissions ?? 'Setup Permissions',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionsWarning(
    BuildContext context,
    RealtimeTranslationLoaded state,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.warning, color: Colors.orange, size: 24.r),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.of(context).missing_permissions ?? 'Missing Permissions',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange[700],
                  ),
                ),
                Text(
                  '${S.of(context).required_permissions ?? 'Required'}: ${state.missingPermissions.join(', ')}',
                  style: TextStyle(fontSize: 14.sp, color: Colors.orange[600]),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: () => _navigateToPermissions(context),
            child: Text(S.of(context).setup ?? 'Setup'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickSettings(
    BuildContext context,
    RealtimeTranslationLoaded state,
  ) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              S.of(context).quick_settings ?? 'Quick Settings',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.of(context).target_language ?? 'Target Language',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        _getLanguageName(state.targetLanguage),
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        S.of(context).source_language ?? 'Source Language',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        _getLanguageName(state.sourceLanguage),
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: SwitchListTile(
                    title: Text(
                      S.of(context).auto_translate_incoming ??
                          'Auto Translate Incoming',
                      style: TextStyle(fontSize: 14.sp),
                    ),
                    value: state.autoTranslateIncoming,
                    onChanged: (value) => context
                        .read<RealtimeTranslationCubit>()
                        .setAutoTranslateIncoming(value),
                    dense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Expanded(
                  child: SwitchListTile(
                    title: Text(
                      S.of(context).auto_translate_outgoing ??
                          'Auto Translate Outgoing',
                      style: TextStyle(fontSize: 14.sp),
                    ),
                    value: state.autoTranslateOutgoing,
                    onChanged: (value) => context
                        .read<RealtimeTranslationCubit>()
                        .setAutoTranslateOutgoing(value),
                    dense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnabledAppsCard(
    BuildContext context,
    RealtimeTranslationLoaded state,
  ) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.of(context).enabled_apps ?? 'Enabled Apps',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${state.enabledApps.length}/${state.installedChatApps.length}',
                  style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            if (state.installedChatApps.isEmpty)
              Text(
                S.of(context).no_chat_apps_found ??
                    'No supported chat apps found',
                style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
              )
            else
              ...state.installedChatApps
                  .take(3)
                  .map(
                    (app) => ListTile(
                      leading: Icon(Icons.chat, size: 24.r),
                      title: Text(app['name'] ?? ''),
                      trailing: Switch(
                        value: state.enabledApps.contains(app['package']),
                        onChanged: (enabled) {
                          final newEnabledApps = Set<String>.from(
                            state.enabledApps,
                          );
                          if (enabled) {
                            newEnabledApps.add(app['package']!);
                          } else {
                            newEnabledApps.remove(app['package']);
                          }
                          context
                              .read<RealtimeTranslationCubit>()
                              .setEnabledApps(newEnabledApps);
                        },
                      ),
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
            if (state.installedChatApps.length > 3)
              TextButton(
                onPressed: () => _navigateToSettings(context),
                child: Text(S.of(context).view_all ?? 'View All'),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveTranslationsCard(
    BuildContext context,
    RealtimeTranslationLoaded state,
  ) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.of(context).active_translations ?? 'Active Translations',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (state.activeTranslations.isNotEmpty)
                  TextButton(
                    onPressed: () => context
                        .read<RealtimeTranslationCubit>()
                        .clearActiveTranslations(),
                    child: Text(S.of(context).clear ?? 'Clear'),
                  ),
              ],
            ),
            SizedBox(height: 12.h),
            if (state.activeTranslations.isEmpty)
              Text(
                S.of(context).no_active_translations ??
                    'No active translations',
                style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
              )
            else
              Text(
                '${state.activeTranslations.length} ${S.of(context).translations_active ?? 'translations active'}',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Theme.of(context).primaryColor,
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _getLanguageName(String code) {
    final languages = {
      'auto': 'Auto Detect',
      'en': 'English',
      'ar': 'Arabic',
      'es': 'Spanish',
      'fr': 'French',
      'de': 'German',
      'it': 'Italian',
      'pt': 'Portuguese',
      'ru': 'Russian',
      'ja': 'Japanese',
      'ko': 'Korean',
      'zh': 'Chinese',
    };
    return languages[code] ?? code.toUpperCase();
  }

  void _navigateToPermissions(BuildContext context) async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: context.read<RealtimeTranslationCubit>(),
          child: const PermissionsSetupPage(),
        ),
      ),
    );

    if (result == true) {
      // Refresh the state after permissions setup
      context.read<RealtimeTranslationCubit>().loadConfiguration();
    }
  }

  void _navigateToSettings(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: context.read<RealtimeTranslationCubit>(),
          child: const TranslationSettingsPage(),
        ),
      ),
    );
  }
}
