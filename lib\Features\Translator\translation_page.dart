import '../../Core/Utils/Extensions/localizations_extension.dart';
import '../../Core/Utils/Widget/default_app_bar.dart';
import '../Home/View/Cubits/home_cubit/home_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:icons_plus/icons_plus.dart';

import '../../Core/Services/translator_model.dart';
import '../Home/View/Widgets/build_trasnlate_card.dart';

class TranslationPage extends StatelessWidget {
  const TranslationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      builder: (context, state) {
        return Scaffold(
          appBar: DefaultAppBar(arowback: true),
          body: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: Column(
                children: [BuildTrasnlateCard(), TranslationBoxLive()],
              ),
            ),
          ),
        );
      },
    );
  }
}

class TranslationBoxLive extends StatefulWidget {
  const TranslationBoxLive({super.key});

  @override
  State<TranslationBoxLive> createState() => _TranslationBoxLiveState();
}

class _TranslationBoxLiveState extends State<TranslationBoxLive> {
  final TextEditingController _controller = TextEditingController();
  String _liveText = '';

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blueAccent),
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          TextFormField(
            controller: _controller,
            textDirection: TextDirection.rtl,
            onChanged: (value) {
              setState(() async {
                final String languageCode = context
                    .read<HomeCubit>()
                    .state
                    .languageSecondTranslate!;
                final String translatedText =
                    await TranslationService.translateText(
                      value,
                      context,
                      languageCode,
                    );
                setState(() {
                  _liveText = translatedText;
                });
              });
            },

            decoration: InputDecoration(
              border: InputBorder.none,
              hintText: context.local.live_translation,
            ),
            style: const TextStyle(fontSize: 16),
          ),

          const Divider(),

          Align(
            alignment: Alignment.topLeft,
            child: Text(
              _liveText,

              textDirection: TextDirection.ltr,
              style: const TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ),

          const SizedBox(height: 12),

          Align(
            alignment: Alignment.bottomLeft,
            child: IconButton(
              onPressed: () {},
              icon: const Icon(Bootstrap.camera_fill, color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
