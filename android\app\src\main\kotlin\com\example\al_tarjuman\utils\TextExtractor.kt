package com.example.al_tarjuman.utils

import android.content.Context
import android.graphics.Rect
import android.os.Bundle
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import com.example.al_tarjuman.models.ChatMessage
import java.util.concurrent.ConcurrentHashMap
import java.util.regex.Pattern

class TextExtractor(private val context: Context? = null) {

    companion object {
        private const val TAG = "TextExtractor"
        private const val MAX_PROCESSED_MESSAGES = 1000
        private const val MESSAGE_CLEANUP_THRESHOLD = 800

        // Patterns for filtering out non-message content
        private val TIME_PATTERN = Pattern.compile("^\\d{1,2}:\\d{2}(\\s?(AM|PM))?$")
        private val DATE_PATTERN = Pattern.compile("^\\d{1,2}/\\d{1,2}/\\d{2,4}$")
        private val STATUS_PATTERN = Pattern.compile("^(Delivered|Read|Sent|Online|Last seen|Typing...)$")
        private val EMOJI_ONLY_PATTERN = Pattern.compile("^[\\p{So}\\p{Cn}\\s]+$")
    }

    private val processedMessages = ConcurrentHashMap<String, Long>()
    private val appDetector = context?.let { AppDetector(it) }
    private var lastCleanupTime = System.currentTimeMillis()
    
    fun extractChatMessages(rootNode: AccessibilityNodeInfo, packageName: String): List<ChatMessage> {
        // Cleanup old processed messages periodically
        cleanupProcessedMessages()

        val messages = mutableListOf<ChatMessage>()
        val config = appDetector?.getAppExtractionConfig(packageName)

        when (packageName) {
            "com.whatsapp", "com.whatsapp.w4b" -> {
                extractWhatsAppMessages(rootNode, messages, config)
            }
            "org.telegram.messenger" -> {
                extractTelegramMessages(rootNode, messages, config)
            }
            "com.facebook.orca" -> {
                extractMessengerMessages(rootNode, messages, config)
            }
            else -> {
                extractGenericMessages(rootNode, messages, config)
            }
        }

        // Filter out already processed messages and add new ones
        val newMessages = messages.filter { message ->
            !processedMessages.containsKey(message.id)
        }

        // Add new messages to processed set with timestamp
        val currentTime = System.currentTimeMillis()
        newMessages.forEach { message ->
            processedMessages[message.id] = currentTime
        }

        return newMessages
    }

    private fun cleanupProcessedMessages() {
        val currentTime = System.currentTimeMillis()

        // Clean up every 5 minutes or when threshold is reached
        if (currentTime - lastCleanupTime > 300000 || processedMessages.size > MESSAGE_CLEANUP_THRESHOLD) {
            val cutoffTime = currentTime - 3600000 // Remove messages older than 1 hour

            val iterator = processedMessages.entries.iterator()
            while (iterator.hasNext()) {
                val entry = iterator.next()
                if (entry.value < cutoffTime) {
                    iterator.remove()
                }
            }

            lastCleanupTime = currentTime
            Log.d(TAG, "Cleaned up processed messages. Current size: ${processedMessages.size}")
        }
    }
    
    private fun extractWhatsAppMessages(
        rootNode: AccessibilityNodeInfo,
        messages: MutableList<ChatMessage>,
        config: AppDetector.AppExtractionConfig?
    ) {
        // WhatsApp specific extraction logic with enhanced detection
        val messageNodes = findMessageNodes(rootNode, config)

        messageNodes.forEach { node ->
            val text = extractTextFromNode(node)
            if (text.isNotBlank() && isValidMessage(text, node)) {
                val bounds = Rect()
                node.getBoundsInScreen(bounds)

                val messageInfo = analyzeWhatsAppMessage(node, text)

                val message = ChatMessage(
                    id = generateMessageId(text, bounds, messageInfo.timestamp),
                    text = text,
                    bounds = bounds,
                    isIncoming = messageInfo.isIncoming,
                    timestamp = messageInfo.timestamp,
                    appPackage = "com.whatsapp"
                )
                messages.add(message)
            }
        }
    }

    private fun analyzeWhatsAppMessage(node: AccessibilityNodeInfo, @Suppress("UNUSED_PARAMETER") text: String): MessageInfo {
        val bounds = Rect()
        node.getBoundsInScreen(bounds)

        // Analyze message bubble position and styling to determine direction
        val isIncoming = determineWhatsAppMessageDirection(node, bounds)

        // Try to extract timestamp from nearby nodes
        val timestamp = extractTimestampFromContext(node) ?: System.currentTimeMillis()

        return MessageInfo(isIncoming, timestamp)
    }

    private fun determineWhatsAppMessageDirection(node: AccessibilityNodeInfo, bounds: Rect): Boolean {
        // WhatsApp incoming messages are typically on the left, outgoing on the right
        val parent = node.parent

        // Check for WhatsApp-specific indicators
        parent?.let { parentNode ->
            val parentBounds = Rect()
            parentNode.getBoundsInScreen(parentBounds)

            // Incoming messages usually have a different background color/style
            val className = parentNode.className?.toString()
            if (className?.contains("incoming") == true || className?.contains("received") == true) {
                return true
            }
            if (className?.contains("outgoing") == true || className?.contains("sent") == true) {
                return false
            }

            // Fallback to position-based detection
            val screenWidth = 1080 // Should be obtained dynamically
            return bounds.left < screenWidth * 0.6
        }

        return bounds.left < 600 // Default fallback
    }
    
    private fun extractTelegramMessages(
        rootNode: AccessibilityNodeInfo,
        messages: MutableList<ChatMessage>,
        config: AppDetector.AppExtractionConfig?
    ) {
        // Telegram specific extraction logic with resource ID targeting
        val messageNodes = findNodesByResourceId(rootNode, "org.telegram.messenger:id/chat_message_text")
            .ifEmpty { findMessageNodes(rootNode, config) }

        messageNodes.forEach { node ->
            val text = extractTextFromNode(node)
            if (text.isNotBlank() && isValidMessage(text, node)) {
                val bounds = Rect()
                node.getBoundsInScreen(bounds)

                val messageInfo = analyzeTelegramMessage(node, text)

                val message = ChatMessage(
                    id = generateMessageId(text, bounds, messageInfo.timestamp),
                    text = text,
                    bounds = bounds,
                    isIncoming = messageInfo.isIncoming,
                    timestamp = messageInfo.timestamp,
                    appPackage = "org.telegram.messenger"
                )
                messages.add(message)
            }
        }
    }

    private fun analyzeTelegramMessage(node: AccessibilityNodeInfo, @Suppress("UNUSED_PARAMETER") text: String): MessageInfo {
        val bounds = Rect()
        node.getBoundsInScreen(bounds)

        // Telegram has better accessibility support, check for specific indicators
        val isIncoming = determineTelegramMessageDirection(node, bounds)
        val timestamp = extractTimestampFromContext(node) ?: System.currentTimeMillis()

        return MessageInfo(isIncoming, timestamp)
    }

    private fun determineTelegramMessageDirection(node: AccessibilityNodeInfo, bounds: Rect): Boolean {
        // Check parent containers for Telegram-specific classes
        var currentNode: AccessibilityNodeInfo? = node
        repeat(3) { // Check up to 3 levels up
            currentNode?.parent?.let { parent ->
                val className = parent.className?.toString()
                if (className?.contains("incoming") == true || className?.contains("in_") == true) {
                    return true
                }
                if (className?.contains("outgoing") == true || className?.contains("out_") == true) {
                    return false
                }
                currentNode = parent
            }
        }

        // Fallback to position-based detection
        return bounds.left < 600
    }
    
    private fun extractMessengerMessages(
        rootNode: AccessibilityNodeInfo,
        messages: MutableList<ChatMessage>,
        config: AppDetector.AppExtractionConfig?
    ) {
        // Facebook Messenger specific extraction logic
        val messageNodes = findMessageNodes(rootNode, config)

        messageNodes.forEach { node ->
            val text = extractTextFromNode(node)
            if (text.isNotBlank() && isValidMessage(text, node)) {
                val bounds = Rect()
                node.getBoundsInScreen(bounds)

                val messageInfo = analyzeGenericMessage(node, text)

                val message = ChatMessage(
                    id = generateMessageId(text, bounds, messageInfo.timestamp),
                    text = text,
                    bounds = bounds,
                    isIncoming = messageInfo.isIncoming,
                    timestamp = messageInfo.timestamp,
                    appPackage = "com.facebook.orca"
                )
                messages.add(message)
            }
        }
    }

    private fun extractGenericMessages(
        rootNode: AccessibilityNodeInfo,
        messages: MutableList<ChatMessage>,
        config: AppDetector.AppExtractionConfig?
    ) {
        // Generic extraction for other apps
        val messageNodes = findMessageNodes(rootNode, config)

        messageNodes.forEach { node ->
            val text = extractTextFromNode(node)
            if (text.isNotBlank() && isValidMessage(text, node)) {
                val bounds = Rect()
                node.getBoundsInScreen(bounds)

                val messageInfo = analyzeGenericMessage(node, text)

                val message = ChatMessage(
                    id = generateMessageId(text, bounds, messageInfo.timestamp),
                    text = text,
                    bounds = bounds,
                    isIncoming = messageInfo.isIncoming,
                    timestamp = messageInfo.timestamp,
                    appPackage = rootNode.packageName?.toString() ?: "unknown"
                )
                messages.add(message)
            }
        }
    }

    private fun analyzeGenericMessage(node: AccessibilityNodeInfo, @Suppress("UNUSED_PARAMETER") text: String): MessageInfo {
        val bounds = Rect()
        node.getBoundsInScreen(bounds)

        val isIncoming = determineGenericMessageDirection(node, bounds)
        val timestamp = extractTimestampFromContext(node) ?: System.currentTimeMillis()

        return MessageInfo(isIncoming, timestamp)
    }

    private fun determineGenericMessageDirection(node: AccessibilityNodeInfo, bounds: Rect): Boolean {
        // Generic message direction detection
        val screenWidth = 1080 // Should be obtained dynamically

        // Check parent containers for direction indicators
        var currentNode: AccessibilityNodeInfo? = node
        repeat(3) {
            currentNode?.parent?.let { parent ->
                val className = parent.className?.toString()
                val contentDesc = parent.contentDescription?.toString()

                // Look for common direction indicators
                if (className?.contains("incoming", ignoreCase = true) == true ||
                    className?.contains("received", ignoreCase = true) == true ||
                    contentDesc?.contains("received", ignoreCase = true) == true) {
                    return true
                }

                if (className?.contains("outgoing", ignoreCase = true) == true ||
                    className?.contains("sent", ignoreCase = true) == true ||
                    contentDesc?.contains("sent", ignoreCase = true) == true) {
                    return false
                }

                currentNode = parent
            }
        }

        // Fallback to position-based detection
        return bounds.left < screenWidth * 0.6
    }
    
    fun findInputField(rootNode: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // Enhanced input field detection with multiple strategies

        // Strategy 1: Look for EditText nodes with message-related hints
        val editTextNodes = findNodesByClassName(rootNode, "android.widget.EditText")

        // First, try to find by hint text
        val hintBasedField = editTextNodes.find { node ->
            node.isEditable && node.isFocusable && hasMessageHint(node)
        }
        if (hintBasedField != null) return hintBasedField

        // Strategy 2: Look for focused or recently focused EditText
        val focusedField = editTextNodes.find { node ->
            node.isEditable && node.isFocused
        }
        if (focusedField != null) return focusedField

        // Strategy 3: Look for EditText in the bottom part of the screen (typical input location)
        val bottomField = editTextNodes.find { node ->
            if (node.isEditable && node.isFocusable) {
                val bounds = Rect()
                node.getBoundsInScreen(bounds)
                bounds.top > 1000 // Assuming screen height > 1200
            } else false
        }
        if (bottomField != null) return bottomField

        // Strategy 4: Look for any editable and focusable EditText
        return editTextNodes.firstOrNull { it.isEditable && it.isFocusable }
    }

    private fun hasMessageHint(node: AccessibilityNodeInfo): Boolean {
        val hint = node.text?.toString()?.lowercase() // AccessibilityNodeInfo doesn't have hint property
        val contentDesc = node.contentDescription?.toString()?.lowercase()

        val messageKeywords = listOf("message", "type", "write", "chat", "text", "send", "aa")

        return messageKeywords.any { keyword ->
            hint?.contains(keyword) == true || contentDesc?.contains(keyword) == true
        }
    }

    fun injectTextToField(inputField: AccessibilityNodeInfo, text: String): Boolean {
        return try {
            // Strategy 1: Try ACTION_SET_TEXT
            val setTextSuccess = try {
                val arguments = Bundle().apply {
                    putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, text)
                }
                inputField.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)
            } catch (e: Exception) {
                Log.w(TAG, "ACTION_SET_TEXT failed, trying alternatives", e)
                false
            }

            if (setTextSuccess) {
                Log.d(TAG, "Text injected successfully using ACTION_SET_TEXT")
                return true
            }

            // Strategy 2: Try to focus first, then set text
            val focusSuccess = inputField.performAction(AccessibilityNodeInfo.ACTION_FOCUS)
            if (focusSuccess) {
                Thread.sleep(100) // Small delay to ensure focus
                val arguments = Bundle().apply {
                    putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, text)
                }
                val setTextAfterFocus = inputField.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)
                if (setTextAfterFocus) {
                    Log.d(TAG, "Text injected successfully after focusing")
                    return true
                }
            }

            // Strategy 3: Try paste action (if text is in clipboard)
            try {
                val clipboardManager = context?.getSystemService(Context.CLIPBOARD_SERVICE) as? android.content.ClipboardManager
                clipboardManager?.let { clipboard ->
                    val clip = android.content.ClipData.newPlainText("translated_text", text)
                    clipboard.setPrimaryClip(clip)

                    val pasteSuccess = inputField.performAction(AccessibilityNodeInfo.ACTION_PASTE)
                    if (pasteSuccess) {
                        Log.d(TAG, "Text injected successfully using paste")
                        return true
                    }
                }
            } catch (e: Exception) {
                Log.w(TAG, "Paste strategy failed", e)
            }

            Log.w(TAG, "All text injection strategies failed")
            false

        } catch (e: Exception) {
            Log.e(TAG, "Failed to inject text", e)
            false
        }
    }

    // Enhanced method to simulate typing (more natural but slower)
    fun simulateTyping(inputField: AccessibilityNodeInfo, text: String): Boolean {
        return try {
            // First focus the field
            inputField.performAction(AccessibilityNodeInfo.ACTION_FOCUS)
            Thread.sleep(100)

            // Clear existing text (using a different approach since ACTION_SELECT_ALL might not be available)
            val clearArguments = Bundle().apply {
                putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, "")
            }
            inputField.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, clearArguments)
            Thread.sleep(50)

            // Set the new text
            val arguments = Bundle().apply {
                putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, text)
            }
            inputField.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)

        } catch (e: Exception) {
            Log.e(TAG, "Failed to simulate typing", e)
            false
        }
    }
    
    private fun findNodesByClassName(rootNode: AccessibilityNodeInfo, className: String): List<AccessibilityNodeInfo> {
        val nodes = mutableListOf<AccessibilityNodeInfo>()
        findNodesByClassNameRecursive(rootNode, className, nodes)
        return nodes
    }
    
    private fun findNodesByClassNameRecursive(node: AccessibilityNodeInfo, className: String, result: MutableList<AccessibilityNodeInfo>) {
        if (node.className?.toString() == className) {
            result.add(node)
        }
        
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                findNodesByClassNameRecursive(child, className, result)
            }
        }
    }
    
    private fun findNodesByResourceId(rootNode: AccessibilityNodeInfo, resourceId: String): List<AccessibilityNodeInfo> {
        val nodes = mutableListOf<AccessibilityNodeInfo>()
        findNodesByResourceIdRecursive(rootNode, resourceId, nodes)
        return nodes
    }
    
    private fun findNodesByResourceIdRecursive(node: AccessibilityNodeInfo, resourceId: String, result: MutableList<AccessibilityNodeInfo>) {
        if (node.viewIdResourceName == resourceId) {
            result.add(node)
        }
        
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                findNodesByResourceIdRecursive(child, resourceId, result)
            }
        }
    }
    
    private fun isLikelyMessage(text: String, node: AccessibilityNodeInfo): Boolean {
        // Filter out UI elements, timestamps, etc.
        if (text.length < 2 || text.length > 1000) return false
        if (text.matches(Regex("^\\d{1,2}:\\d{2}.*"))) return false // Timestamps
        if (text.matches(Regex("^[\\d\\s:]+$"))) return false // Only numbers and time
        
        // Check if parent or node has message-like characteristics
        val bounds = Rect()
        node.getBoundsInScreen(bounds)
        
        return bounds.width() > 100 && bounds.height() > 20
    }
    
    private fun determineMessageDirection(node: AccessibilityNodeInfo): Boolean {
        // Heuristic to determine if message is incoming (true) or outgoing (false)
        val bounds = Rect()
        node.getBoundsInScreen(bounds)
        
        // Generally, incoming messages are on the left, outgoing on the right
        // This is a simplified heuristic and may need app-specific adjustments
        val screenWidth = 1080 // This should be obtained dynamically
        return bounds.left < screenWidth * 0.6
    }
    
    // Helper methods and data classes
    private data class MessageInfo(
        val isIncoming: Boolean,
        val timestamp: Long
    )

    private fun findMessageNodes(rootNode: AccessibilityNodeInfo, config: AppDetector.AppExtractionConfig?): List<AccessibilityNodeInfo> {
        val messageNodes = mutableListOf<AccessibilityNodeInfo>()

        // Try to find nodes using app-specific configuration
        config?.let { appConfig ->
            // First try to find by resource IDs
            appConfig.messageIndicators.forEach { indicator ->
                if (indicator.contains(":id/")) {
                    messageNodes.addAll(findNodesByResourceId(rootNode, indicator))
                }
            }

            // If no specific IDs found, fall back to class-based search
            if (messageNodes.isEmpty()) {
                messageNodes.addAll(findNodesByClassName(rootNode, appConfig.messageTextClass))
            }
        }

        // Final fallback to generic TextView search
        if (messageNodes.isEmpty()) {
            messageNodes.addAll(findNodesByClassName(rootNode, "android.widget.TextView"))
        }

        return messageNodes
    }

    private fun extractTextFromNode(node: AccessibilityNodeInfo): String {
        return node.text?.toString()?.trim() ?: ""
    }

    private fun isValidMessage(text: String, node: AccessibilityNodeInfo): Boolean {
        // Enhanced message validation
        if (text.length < 1 || text.length > 4000) return false

        // Filter out timestamps, status messages, and UI elements
        if (TIME_PATTERN.matcher(text).matches()) return false
        if (DATE_PATTERN.matcher(text).matches()) return false
        if (STATUS_PATTERN.matcher(text).matches()) return false

        // Filter out emoji-only messages (optional)
        if (text.length < 10 && EMOJI_ONLY_PATTERN.matcher(text).matches()) return false

        // Check if the node has message-like characteristics
        val bounds = Rect()
        node.getBoundsInScreen(bounds)

        // Message should have reasonable dimensions
        if (bounds.width() < 50 || bounds.height() < 20) return false

        // Check if it's likely a clickable message bubble
        return isLikelyMessageBubble(node, bounds)
    }

    private fun isLikelyMessageBubble(node: AccessibilityNodeInfo, @Suppress("UNUSED_PARAMETER") bounds: Rect): Boolean {
        // Check parent containers for message-like properties
        var currentNode: AccessibilityNodeInfo? = node
        repeat(3) { // Check up to 3 levels up
            currentNode?.parent?.let { parent ->
                val className = parent.className?.toString()

                // Look for message container indicators
                if (className?.contains("message", ignoreCase = true) == true ||
                    className?.contains("chat", ignoreCase = true) == true ||
                    className?.contains("bubble", ignoreCase = true) == true) {
                    return true
                }

                // Check if parent is clickable (messages are usually clickable)
                if (parent.isClickable) {
                    val parentBounds = Rect()
                    parent.getBoundsInScreen(parentBounds)

                    // Parent should be reasonably sized for a message
                    if (parentBounds.width() > 100 && parentBounds.height() > 30) {
                        return true
                    }
                }

                currentNode = parent
            }
        }

        return false
    }

    private fun extractTimestampFromContext(node: AccessibilityNodeInfo): Long? {
        // Try to find timestamp in nearby nodes
        val parent = node.parent ?: return null

        for (i in 0 until parent.childCount) {
            val sibling = parent.getChild(i) ?: continue
            val siblingText = sibling.text?.toString()

            if (siblingText != null && TIME_PATTERN.matcher(siblingText).matches()) {
                // Convert time string to timestamp (simplified)
                return parseTimeString(siblingText)
            }
        }

        return null
    }

    private fun parseTimeString(timeString: String): Long {
        // Simplified time parsing - in a real implementation, you'd want more robust parsing
        try {
            val parts = timeString.split(":")
            if (parts.size >= 2) {
                val hour = parts[0].toInt()
                val minute = parts[1].replace(Regex("[^\\d]"), "").toInt()

                // Create timestamp for today with the given time
                val calendar = java.util.Calendar.getInstance()
                calendar.set(java.util.Calendar.HOUR_OF_DAY, hour)
                calendar.set(java.util.Calendar.MINUTE, minute)
                calendar.set(java.util.Calendar.SECOND, 0)

                return calendar.timeInMillis
            }
        } catch (e: Exception) {
            Log.w(TAG, "Failed to parse time string: $timeString", e)
        }

        return System.currentTimeMillis()
    }

    private fun generateMessageId(text: String, bounds: Rect, timestamp: Long = System.currentTimeMillis()): String {
        return "${text.hashCode()}_${bounds.left}_${bounds.top}_${timestamp / 1000}"
    }
}
