package com.example.al_tarjuman.utils

import android.graphics.Rect
import android.os.Bundle
import android.util.Log
import android.view.accessibility.AccessibilityNodeInfo
import com.example.al_tarjuman.models.ChatMessage

class TextExtractor {
    
    companion object {
        private const val TAG = "TextExtractor"
    }
    
    private val processedMessages = mutableSetOf<String>()
    
    fun extractChatMessages(rootNode: AccessibilityNodeInfo, packageName: String): List<ChatMessage> {
        val messages = mutableListOf<ChatMessage>()
        
        when (packageName) {
            "com.whatsapp", "com.whatsapp.w4b" -> {
                extractWhatsAppMessages(rootNode, messages)
            }
            "org.telegram.messenger" -> {
                extractTelegramMessages(rootNode, messages)
            }
            "com.facebook.orca" -> {
                extractMessengerMessages(rootNode, messages)
            }
            else -> {
                extractGenericMessages(rootNode, messages)
            }
        }
        
        return messages.filter { !processedMessages.contains(it.id) }.also { newMessages ->
            newMessages.forEach { processedMessages.add(it.id) }
        }
    }
    
    private fun extractWhatsAppMessages(rootNode: AccessibilityNodeInfo, messages: MutableList<ChatMessage>) {
        // WhatsApp specific extraction logic
        findNodesByClassName(rootNode, "android.widget.TextView").forEach { node ->
            val text = node.text?.toString()
            if (!text.isNullOrBlank() && isLikelyMessage(text, node)) {
                val bounds = Rect()
                node.getBoundsInScreen(bounds)
                
                val message = ChatMessage(
                    id = generateMessageId(text, bounds),
                    text = text,
                    bounds = bounds,
                    isIncoming = determineMessageDirection(node),
                    timestamp = System.currentTimeMillis(),
                    appPackage = "com.whatsapp"
                )
                messages.add(message)
            }
        }
    }
    
    private fun extractTelegramMessages(rootNode: AccessibilityNodeInfo, messages: MutableList<ChatMessage>) {
        // Telegram specific extraction logic
        findNodesByResourceId(rootNode, "org.telegram.messenger:id/chat_message_text").forEach { node ->
            val text = node.text?.toString()
            if (!text.isNullOrBlank()) {
                val bounds = Rect()
                node.getBoundsInScreen(bounds)
                
                val message = ChatMessage(
                    id = generateMessageId(text, bounds),
                    text = text,
                    bounds = bounds,
                    isIncoming = determineMessageDirection(node),
                    timestamp = System.currentTimeMillis(),
                    appPackage = "org.telegram.messenger"
                )
                messages.add(message)
            }
        }
    }
    
    private fun extractMessengerMessages(rootNode: AccessibilityNodeInfo, messages: MutableList<ChatMessage>) {
        // Facebook Messenger specific extraction logic
        findNodesByClassName(rootNode, "android.widget.TextView").forEach { node ->
            val text = node.text?.toString()
            if (!text.isNullOrBlank() && isLikelyMessage(text, node)) {
                val bounds = Rect()
                node.getBoundsInScreen(bounds)
                
                val message = ChatMessage(
                    id = generateMessageId(text, bounds),
                    text = text,
                    bounds = bounds,
                    isIncoming = determineMessageDirection(node),
                    timestamp = System.currentTimeMillis(),
                    appPackage = "com.facebook.orca"
                )
                messages.add(message)
            }
        }
    }
    
    private fun extractGenericMessages(rootNode: AccessibilityNodeInfo, messages: MutableList<ChatMessage>) {
        // Generic extraction for other apps
        findNodesByClassName(rootNode, "android.widget.TextView").forEach { node ->
            val text = node.text?.toString()
            if (!text.isNullOrBlank() && isLikelyMessage(text, node)) {
                val bounds = Rect()
                node.getBoundsInScreen(bounds)
                
                val message = ChatMessage(
                    id = generateMessageId(text, bounds),
                    text = text,
                    bounds = bounds,
                    isIncoming = determineMessageDirection(node),
                    timestamp = System.currentTimeMillis(),
                    appPackage = rootNode.packageName?.toString() ?: "unknown"
                )
                messages.add(message)
            }
        }
    }
    
    fun findInputField(rootNode: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // Look for EditText nodes that are likely input fields
        val editTextNodes = findNodesByClassName(rootNode, "android.widget.EditText")
        
        return editTextNodes.find { node ->
            node.isEditable && node.isFocusable && 
            (node.hint?.contains("message", ignoreCase = true) == true ||
             node.hint?.contains("type", ignoreCase = true) == true ||
             node.contentDescription?.contains("message", ignoreCase = true) == true)
        } ?: editTextNodes.firstOrNull { it.isEditable && it.isFocusable }
    }
    
    fun injectTextToField(inputField: AccessibilityNodeInfo, text: String): Boolean {
        return try {
            val arguments = Bundle().apply {
                putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, text)
            }
            inputField.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, arguments)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to inject text", e)
            false
        }
    }
    
    private fun findNodesByClassName(rootNode: AccessibilityNodeInfo, className: String): List<AccessibilityNodeInfo> {
        val nodes = mutableListOf<AccessibilityNodeInfo>()
        findNodesByClassNameRecursive(rootNode, className, nodes)
        return nodes
    }
    
    private fun findNodesByClassNameRecursive(node: AccessibilityNodeInfo, className: String, result: MutableList<AccessibilityNodeInfo>) {
        if (node.className?.toString() == className) {
            result.add(node)
        }
        
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                findNodesByClassNameRecursive(child, className, result)
            }
        }
    }
    
    private fun findNodesByResourceId(rootNode: AccessibilityNodeInfo, resourceId: String): List<AccessibilityNodeInfo> {
        val nodes = mutableListOf<AccessibilityNodeInfo>()
        findNodesByResourceIdRecursive(rootNode, resourceId, nodes)
        return nodes
    }
    
    private fun findNodesByResourceIdRecursive(node: AccessibilityNodeInfo, resourceId: String, result: MutableList<AccessibilityNodeInfo>) {
        if (node.viewIdResourceName == resourceId) {
            result.add(node)
        }
        
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                findNodesByResourceIdRecursive(child, resourceId, result)
            }
        }
    }
    
    private fun isLikelyMessage(text: String, node: AccessibilityNodeInfo): Boolean {
        // Filter out UI elements, timestamps, etc.
        if (text.length < 2 || text.length > 1000) return false
        if (text.matches(Regex("^\\d{1,2}:\\d{2}.*"))) return false // Timestamps
        if (text.matches(Regex("^[\\d\\s:]+$"))) return false // Only numbers and time
        
        // Check if parent or node has message-like characteristics
        val bounds = Rect()
        node.getBoundsInScreen(bounds)
        
        return bounds.width() > 100 && bounds.height() > 20
    }
    
    private fun determineMessageDirection(node: AccessibilityNodeInfo): Boolean {
        // Heuristic to determine if message is incoming (true) or outgoing (false)
        val bounds = Rect()
        node.getBoundsInScreen(bounds)
        
        // Generally, incoming messages are on the left, outgoing on the right
        // This is a simplified heuristic and may need app-specific adjustments
        val screenWidth = 1080 // This should be obtained dynamically
        return bounds.left < screenWidth * 0.6
    }
    
    private fun generateMessageId(text: String, bounds: Rect): String {
        return "${text.hashCode()}_${bounds.left}_${bounds.top}"
    }
}
