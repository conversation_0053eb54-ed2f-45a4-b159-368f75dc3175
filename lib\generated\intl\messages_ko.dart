// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ko locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ko';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts":
            MessageLookupByLibrary.simpleMessage(
                "번역기가 인공지능을 사용하여 텍스트를 자동으로 번역하고 선택한 언어로 전송하도록 허용합니다."),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "번역기가 인공지능을 사용하여 오디오 녹음을 자동으로 번역하고 선택한 언어로 전송하도록 허용합니다."),
        "chat_now":
            MessageLookupByLibrary.simpleMessage("휴대폰 앱에 대한 액세스를 허용합니다."),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "번역기로 즉시적이고 부드러운 번역 경험을 즐기세요. 텍스트, 단어, 오디오 녹음을 선호하는 언어로 쉽고 빠르게 변환할 수 있습니다. 높은 정확도와 편안한 경험을 보장하는 고급 인공지능 기술 덕분입니다."),
        "enable_camera":
            MessageLookupByLibrary.simpleMessage("번역기가 카메라를 사용하도록 허용"),
        "enable_microphone":
            MessageLookupByLibrary.simpleMessage("번역기가 마이크를 사용하도록 허용"),
        "enable_pick_file":
            MessageLookupByLibrary.simpleMessage("번역기가 갤러리에서 파일을 가져오도록 허용"),
        "enable_pick_image":
            MessageLookupByLibrary.simpleMessage("번역기가 갤러리에서 이미지를 가져오도록 허용"),
        "language": MessageLookupByLibrary.simpleMessage("언어"),
        "live_translation":
            MessageLookupByLibrary.simpleMessage("여기에 번역을 작성하세요..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "인공지능을 사용하여 오디오 녹음을 번역된 녹음으로 변환합니다."),
        "name_app": MessageLookupByLibrary.simpleMessage("번역기"),
        "search_language": MessageLookupByLibrary.simpleMessage("언어 검색..."),
        "symbol_appears_on_the_screen":
            MessageLookupByLibrary.simpleMessage("기호가 화면에 나타납니다"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "인공지능의 힘으로 번역기를 사용하여 즉시 번역하세요."),
        "translate_now":
            MessageLookupByLibrary.simpleMessage("지금 번역기를 사용하여 텍스트를 번역하세요.")
      };
}
