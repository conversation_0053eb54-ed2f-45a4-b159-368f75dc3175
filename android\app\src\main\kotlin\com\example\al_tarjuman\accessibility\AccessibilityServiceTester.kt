package com.example.al_tarjuman.accessibility

import android.content.Context
import android.util.Log
import com.example.al_tarjuman.utils.AppDetector
import com.example.al_tarjuman.utils.TextExtractor

/**
 * Utility class for testing AccessibilityService functionality
 */
class AccessibilityServiceTester(private val context: Context) {
    
    companion object {
        private const val TAG = "AccessibilityTester"
    }
    
    private val appDetector = AppDetector(context)
    private val textExtractor = TextExtractor(context)
    
    /**
     * Test app detection functionality
     */
    fun testAppDetection(): TestResult {
        val results = mutableListOf<String>()
        var passed = 0
        var total = 0
        
        try {
            // Test supported apps detection
            val supportedApps = AppDetector.SUPPORTED_APPS.keys
            results.add("Testing ${supportedApps.size} supported apps...")
            
            supportedApps.forEach { packageName ->
                total++
                val isSupported = appDetector.isSupportedChatApp(packageName)
                val isInstalled = appDetector.isAppInstalled(packageName)
                
                if (isSupported) {
                    passed++
                    results.add("✓ $packageName: supported=${isSupported}, installed=${isInstalled}")
                } else {
                    results.add("✗ $packageName: should be supported but isn't")
                }
            }
            
            // Test installed apps
            val installedApps = appDetector.getInstalledSupportedApps()
            results.add("\nInstalled supported apps: ${installedApps.size}")
            installedApps.forEach { app ->
                results.add("  - ${app.displayName} (${app.packageName}) v${app.versionName}")
            }
            
        } catch (e: Exception) {
            results.add("Error during app detection test: ${e.message}")
            Log.e(TAG, "App detection test failed", e)
        }
        
        return TestResult(
            testName = "App Detection",
            passed = passed,
            total = total,
            details = results
        )
    }
    
    /**
     * Test accessibility service status
     */
    fun testAccessibilityService(): TestResult {
        val results = mutableListOf<String>()
        var passed = 0
        var total = 3
        
        try {
            // Test service instance
            val serviceInstance = TranslatorAccessibilityService.instance
            if (serviceInstance != null) {
                passed++
                results.add("✓ AccessibilityService instance is available")
                
                // Test service status
                val status = serviceInstance.getServiceStatus()
                results.add("Service status:")
                status.forEach { (key, value) ->
                    results.add("  - $key: $value")
                }
                
                // Test current app detection
                val currentApp = serviceInstance.getCurrentApp()
                if (currentApp != null) {
                    passed++
                    results.add("✓ Current app detected: $currentApp")
                    
                    val isSupported = serviceInstance.isCurrentAppSupported()
                    results.add("  - Is supported: $isSupported")
                } else {
                    results.add("✗ Could not detect current app")
                }
                
                // Test input field detection
                val inputField = serviceInstance.findInputField()
                if (inputField != null) {
                    passed++
                    results.add("✓ Input field found")
                    results.add("  - Class: ${inputField.className}")
                    results.add("  - Editable: ${inputField.isEditable}")
                    results.add("  - Focusable: ${inputField.isFocusable}")
                } else {
                    results.add("✗ No input field found (may be normal if not in a chat app)")
                }
                
            } else {
                results.add("✗ AccessibilityService instance not available")
                results.add("  Make sure the accessibility service is enabled in settings")
            }
            
        } catch (e: Exception) {
            results.add("Error during accessibility service test: ${e.message}")
            Log.e(TAG, "Accessibility service test failed", e)
        }
        
        return TestResult(
            testName = "Accessibility Service",
            passed = passed,
            total = total,
            details = results
        )
    }
    
    /**
     * Test text extraction configuration
     */
    fun testTextExtractionConfig(): TestResult {
        val results = mutableListOf<String>()
        var passed = 0
        var total = 0
        
        try {
            val supportedApps = AppDetector.SUPPORTED_APPS.keys
            results.add("Testing text extraction configuration for ${supportedApps.size} apps...")
            
            supportedApps.forEach { packageName ->
                total++
                try {
                    val config = appDetector.getAppExtractionConfig(packageName)
                    val appInfo = appDetector.getAppInfo(packageName)
                    
                    passed++
                    results.add("✓ ${appInfo?.displayName} ($packageName):")
                    results.add("  - Message container: ${config.messageContainerClass}")
                    results.add("  - Message text: ${config.messageTextClass}")
                    results.add("  - Input field: ${config.inputFieldClass}")
                    results.add("  - Input hint: ${config.inputFieldHint}")
                    results.add("  - Message indicators: ${config.messageIndicators.joinToString(", ")}")
                    
                    val supportsAdvanced = appDetector.supportsAdvancedFeatures(packageName)
                    results.add("  - Advanced features: $supportsAdvanced")
                    
                } catch (e: Exception) {
                    results.add("✗ Error getting config for $packageName: ${e.message}")
                }
            }
            
        } catch (e: Exception) {
            results.add("Error during text extraction config test: ${e.message}")
            Log.e(TAG, "Text extraction config test failed", e)
        }
        
        return TestResult(
            testName = "Text Extraction Configuration",
            passed = passed,
            total = total,
            details = results
        )
    }
    
    /**
     * Run all tests
     */
    fun runAllTests(): List<TestResult> {
        Log.d(TAG, "Running all accessibility service tests...")
        
        return listOf(
            testAppDetection(),
            testAccessibilityService(),
            testTextExtractionConfig()
        )
    }
    
    /**
     * Generate a comprehensive test report
     */
    fun generateTestReport(): String {
        val results = runAllTests()
        val report = StringBuilder()
        
        report.appendLine("=== Accessibility Service Test Report ===")
        report.appendLine("Generated at: ${java.util.Date()}")
        report.appendLine()
        
        var totalPassed = 0
        var totalTests = 0
        
        results.forEach { result ->
            totalPassed += result.passed
            totalTests += result.total
            
            report.appendLine("## ${result.testName}")
            report.appendLine("Status: ${result.passed}/${result.total} passed")
            report.appendLine()
            
            result.details.forEach { detail ->
                report.appendLine(detail)
            }
            report.appendLine()
        }
        
        report.appendLine("=== Summary ===")
        report.appendLine("Overall: $totalPassed/$totalTests tests passed")
        val percentage = if (totalTests > 0) (totalPassed * 100) / totalTests else 0
        report.appendLine("Success rate: $percentage%")
        
        return report.toString()
    }
    
    data class TestResult(
        val testName: String,
        val passed: Int,
        val total: Int,
        val details: List<String>
    ) {
        val isSuccess: Boolean get() = passed == total
        val successRate: Double get() = if (total > 0) (passed.toDouble() / total) * 100 else 0.0
    }
}
