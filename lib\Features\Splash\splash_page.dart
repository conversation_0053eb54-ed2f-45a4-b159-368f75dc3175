import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../Config/Assets/image_svg.dart';
import '../../Config/Routes/app_routes.dart';
import '../../Core/Resources/colors.dart';
import '../../Core/Resources/text_style.dart';
import '../../Core/Utils/Animations/splash_animation.dart';
import '../../Core/Utils/Extensions/localizations_extension.dart';
import '../../Core/Utils/Widget/build_image.dart';
import '../../main.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
  late SplashAnimation _splashAnimation;

@override
void initState() {
  super.initState();
  _splashAnimation = SplashAnimation(vsync: this);
  _splashAnimation.controller.addStatusListener((status) {
    if (status == AnimationStatus.completed) {
      kNavigationService.clearAndNavigateTo(AppRoutes.home);
    }
  });
  _splashAnimation.startAnimation();
}

  @override
  void dispose() {
    _splashAnimation.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          spacing: 40.h,
          children: [
            _splashAnimation.buildAnimatedSplashImage(
              child: BuildImageAssets(svg: AppImagesSvg.splash, width: .75.sw),
            ),
            _splashAnimation.buildAnimatedAppNameRow(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                spacing: 6.w,
                children: [
                  Text(context.local.name_app, style: AppTextStyles.h4Bold.copyWith(color: AppColor.primaryColors)),
                  BuildImageAssets(svg: AppImagesSvg.logo, ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
