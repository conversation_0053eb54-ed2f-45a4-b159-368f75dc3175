import 'package:installed_apps/installed_apps.dart';
import 'package:flutter/foundation.dart';

class AppDetectionService {
  static const Map<String, List<String>> _appPackageMap = {
    'WhatsApp': [
      'com.whatsapp',
      'com.whatsapp.w4b', 
    ],
    'Messenger': [
      'com.facebook.orca',
      'com.facebook.mlite', 
    ],
    'Telegram': [
      'org.telegram.messenger',
      'org.telegram.plus',
      'org.thunderdog.challegram',
    ],
    'Instagram': ['com.instagram.android', 'com.instagram.lite'],
    'Facebook': ['com.facebook.katana', 'com.facebook.lite'],
    'Botim': ['im.thebot.messenger'],
    'IMO': ['com.imo.android.imoim'],
    'Snapchat': ['com.snapchat.android'],
    'Viber': ['com.viber.voip'],
    'Google Chat': [
      'com.google.android.apps.dynamite',
      'com.google.android.talk',
    ],
    'LINE': ['jp.naver.line.android'],
  };

  static Future<bool> isAppInstalled(String appName) async {
    try {
      final packageNames = _appPackageMap[appName];
      if (packageNames == null || packageNames.isEmpty) {
        return false;
      }

      final installedApps = await InstalledApps.getInstalledApps(true, true);

      for (final packageName in packageNames) {
        final isInstalled = installedApps.any(
          (app) => app.packageName == packageName,
        );
        if (isInstalled) {
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  static Future<List<String>> getInstalledSocialApps() async {
    final List<String> installedApps = [];

    try {
      final allInstalledApps = await InstalledApps.getInstalledApps(true, true);
      final installedPackageNames = allInstalledApps
          .map((app) => app.packageName)
          .toSet();

      for (final appName in _appPackageMap.keys) {
        final packageNames = _appPackageMap[appName]!;

        final isInstalled = packageNames.any(
          (packageName) => installedPackageNames.contains(packageName),
        );

        if (isInstalled) {
          installedApps.add(appName);
        }
      }
      return installedApps;
    } catch (e) {
      return [];
    }
  }

 

  /// Returns false on platforms where app detection is not available
  static bool get isAppDetectionSupported => defaultTargetPlatform == TargetPlatform.android;

  /// Get all supported social app names
  static List<String> get supportedApps => _appPackageMap.keys.toList();

  /// Get package names for a specific app
  static List<String>? getPackageNames(String appName) => _appPackageMap[appName];
}
