// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a th locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'th';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts": MessageLookupByLibrary.simpleMessage(
            "อนุญาตให้นักแปลแปลข้อความโดยอัตโนมัติและส่งในภาษาที่เลือกโดยใช้ปัญญาประดิษฐ์"),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "อนุญาตให้นักแปลแปลการบันทึกเสียงของคุณโดยอัตโนมัติและส่งในภาษาที่เลือกโดยใช้ปัญญาประดิษฐ์"),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "อนุญาตให้เข้าถึงแอปพลิเคชันโทรศัพท์"),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "เพลิดเพลินกับประสบการณ์การแปลที่ทันทีและราบรื่นด้วยนักแปล ที่คุณสามารถแปลงข้อความ คำ และการบันทึกเสียงเป็นภาษาที่คุณต้องการได้อย่างง่ายดายและรวดเร็ว ด้วยเทคโนโลยีปัญญาประดิษฐ์ขั้นสูงที่รับประกันความแม่นยำสูงและประสบการณ์ที่สะดวกสบาย"),
        "enable_camera":
            MessageLookupByLibrary.simpleMessage("อนุญาตให้นักแปลใช้กล้อง"),
        "enable_microphone":
            MessageLookupByLibrary.simpleMessage("อนุญาตให้นักแปลใช้ไมโครโฟน"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "อนุญาตให้นักแปลนำเข้าไฟล์จากแกลเลอรี"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "อนุญาตให้นักแปลนำเข้ารูปภาพจากแกลเลอรี"),
        "language": MessageLookupByLibrary.simpleMessage("ภาษา"),
        "live_translation":
            MessageLookupByLibrary.simpleMessage("เขียนการแปลที่นี่..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "แปลงการบันทึกเสียงเป็นการบันทึกที่แปลแล้วโดยใช้ปัญญาประดิษฐ์"),
        "name_app": MessageLookupByLibrary.simpleMessage("นักแปล"),
        "search_language": MessageLookupByLibrary.simpleMessage("ค้นหาภาษา..."),
        "symbol_appears_on_the_screen":
            MessageLookupByLibrary.simpleMessage("สัญลักษณ์ปรากฏบนหน้าจอ"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "แปลทันทีด้วยนักแปล ด้วยพลังของปัญญาประดิษฐ์"),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "ใช้นักแปลเพื่อแปลข้อความตอนนี้")
      };
}
