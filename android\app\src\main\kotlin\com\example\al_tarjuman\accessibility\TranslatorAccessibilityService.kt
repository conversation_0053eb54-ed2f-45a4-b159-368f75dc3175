package com.example.al_tarjuman.accessibility

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.content.Intent
import android.graphics.Rect
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import com.example.al_tarjuman.channels.PlatformChannelManager
import com.example.al_tarjuman.models.ChatMessage
import com.example.al_tarjuman.overlay.OverlayService
import com.example.al_tarjuman.utils.TextExtractor
import com.example.al_tarjuman.utils.AppDetector
import java.util.concurrent.ConcurrentHashMap

class TranslatorAccessibilityService : AccessibilityService() {
    
    companion object {
        private const val TAG = "TranslatorAccessibility"
        var instance: TranslatorAccessibilityService? = null
    }
    
    private lateinit var textExtractor: TextExtractor
    private lateinit var appDetector: AppDetector
    private var isServiceEnabled = false
    private val mainHandler = Handler(Looper.getMainLooper())
    private val processedEvents = ConcurrentHashMap<String, Long>()
    private var lastEventTime = 0L
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        textExtractor = TextExtractor(this)
        appDetector = AppDetector(this)
        Log.d(TAG, "TranslatorAccessibilityService created")
    }
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        isServiceEnabled = true
        
        // Configure service info
        val info = AccessibilityServiceInfo().apply {
            eventTypes = AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED or
                        AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED or
                        AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED
            feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC
            flags = AccessibilityServiceInfo.FLAG_REPORT_VIEW_IDS or
                   AccessibilityServiceInfo.FLAG_RETRIEVE_INTERACTIVE_WINDOWS
            notificationTimeout = 100
        }
        serviceInfo = info
        
        Log.d(TAG, "Accessibility service connected and configured")
        
        // Notify Flutter that service is ready
        PlatformChannelManager.notifyAccessibilityServiceStatus(true)
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (!isServiceEnabled || event == null) return

        try {
            // Throttle events to prevent excessive processing
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastEventTime < 100) return // Minimum 100ms between events
            lastEventTime = currentTime

            when (event.eventType) {
                AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED,
                AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED -> {
                    handleTextChange(event)
                }
                AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
                    handleWindowStateChange(event)
                }
                AccessibilityEvent.TYPE_VIEW_FOCUSED -> {
                    handleViewFocused(event)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing accessibility event", e)
        }
    }
    
    private fun handleTextChange(event: AccessibilityEvent) {
        val packageName = event.packageName?.toString() ?: return

        // Only process supported chat apps
        if (!appDetector.isSupportedChatApp(packageName)) return

        // Create a unique event ID to prevent duplicate processing
        val eventId = "${packageName}_${event.eventTime}_${event.text.hashCode()}"
        if (processedEvents.containsKey(eventId)) return

        processedEvents[eventId] = System.currentTimeMillis()

        // Clean up old processed events
        cleanupProcessedEvents()

        // Process in background to avoid blocking the main thread
        mainHandler.post {
            try {
                val rootNode = rootInActiveWindow ?: return@post
                val extractedMessages = textExtractor.extractChatMessages(rootNode, packageName)

                extractedMessages.forEach { message ->
                    processChatMessage(message, packageName)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error extracting messages", e)
            }
        }
    }

    private fun cleanupProcessedEvents() {
        val currentTime = System.currentTimeMillis()
        val cutoffTime = currentTime - 60000 // Remove events older than 1 minute

        val iterator = processedEvents.entries.iterator()
        while (iterator.hasNext()) {
            val entry = iterator.next()
            if (entry.value < cutoffTime) {
                iterator.remove()
            }
        }
    }
    
    private fun handleWindowStateChange(event: AccessibilityEvent) {
        val packageName = event.packageName?.toString() ?: return
        Log.d(TAG, "Window state changed for: $packageName")

        if (appDetector.isSupportedChatApp(packageName)) {
            // Notify Flutter about app focus change
            PlatformChannelManager.notifyAppFocusChange(packageName, true)

            // Log app information for debugging
            val appInfo = appDetector.getAppInfo(packageName)
            Log.d(TAG, "Focused app: ${appInfo?.displayName} (${packageName})")
        }
    }

    private fun handleViewFocused(event: AccessibilityEvent) {
        val packageName = event.packageName?.toString() ?: return

        if (appDetector.isSupportedChatApp(packageName)) {
            // Check if an input field was focused
            val source = event.source
            if (source != null && source.className?.toString() == "android.widget.EditText") {
                Log.d(TAG, "Input field focused in $packageName")
                // Could be used to prepare for text injection
            }
        }
    }
    
    private fun processChatMessage(message: ChatMessage, packageName: String) {
        Log.d(TAG, "Processing message: ${message.text.take(50)}... from $packageName")

        // Send message to Flutter for translation
        try {
            PlatformChannelManager.sendMessageForTranslation(message, packageName)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send message for translation", e)
        }
    }
    
    override fun onInterrupt() {
        Log.d(TAG, "Accessibility service interrupted")
        isServiceEnabled = false
    }
    
    override fun onDestroy() {
        super.onDestroy()
        instance = null
        isServiceEnabled = false
        PlatformChannelManager.notifyAccessibilityServiceStatus(false)
        Log.d(TAG, "TranslatorAccessibilityService destroyed")
    }
    
    // Public methods for external access
    fun findInputField(): AccessibilityNodeInfo? {
        return try {
            val rootNode = rootInActiveWindow ?: return null
            textExtractor.findInputField(rootNode)
        } catch (e: Exception) {
            Log.e(TAG, "Error finding input field", e)
            null
        }
    }

    fun injectText(text: String): Boolean {
        return try {
            val inputField = findInputField() ?: return false
            Log.d(TAG, "Injecting text: ${text.take(50)}...")
            textExtractor.injectTextToField(inputField, text)
        } catch (e: Exception) {
            Log.e(TAG, "Error injecting text", e)
            false
        }
    }

    fun simulateTyping(text: String): Boolean {
        return try {
            val inputField = findInputField() ?: return false
            Log.d(TAG, "Simulating typing: ${text.take(50)}...")
            textExtractor.simulateTyping(inputField, text)
        } catch (e: Exception) {
            Log.e(TAG, "Error simulating typing", e)
            false
        }
    }

    fun getCurrentApp(): String? {
        return try {
            val rootNode = rootInActiveWindow
            rootNode?.packageName?.toString()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current app", e)
            null
        }
    }

    fun isCurrentAppSupported(): Boolean {
        val currentApp = getCurrentApp()
        return currentApp?.let { appDetector.isSupportedChatApp(it) } ?: false
    }

    fun getServiceStatus(): Map<String, Any> {
        return mapOf(
            "isEnabled" to isServiceEnabled,
            "currentApp" to (getCurrentApp() ?: "unknown"),
            "isCurrentAppSupported" to isCurrentAppSupported(),
            "processedEventsCount" to processedEvents.size,
            "lastEventTime" to lastEventTime
        )
    }
}
