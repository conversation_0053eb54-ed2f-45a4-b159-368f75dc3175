package com.example.al_tarjuman.accessibility

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.content.Intent
import android.graphics.Rect
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import com.example.al_tarjuman.channels.PlatformChannelManager
import com.example.al_tarjuman.models.ChatMessage
import com.example.al_tarjuman.overlay.OverlayService
import com.example.al_tarjuman.utils.TextExtractor

class TranslatorAccessibilityService : AccessibilityService() {
    
    companion object {
        private const val TAG = "TranslatorAccessibility"
        var instance: TranslatorAccessibilityService? = null
    }
    
    private lateinit var textExtractor: TextExtractor
    private var isServiceEnabled = false
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        textExtractor = TextExtractor()
        Log.d(TAG, "TranslatorAccessibilityService created")
    }
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        isServiceEnabled = true
        
        // Configure service info
        val info = AccessibilityServiceInfo().apply {
            eventTypes = AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED or
                        AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED or
                        AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED
            feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC
            flags = AccessibilityServiceInfo.FLAG_REPORT_VIEW_IDS or
                   AccessibilityServiceInfo.FLAG_RETRIEVE_INTERACTIVE_WINDOWS
            notificationTimeout = 100
        }
        serviceInfo = info
        
        Log.d(TAG, "Accessibility service connected and configured")
        
        // Notify Flutter that service is ready
        PlatformChannelManager.notifyAccessibilityServiceStatus(true)
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (!isServiceEnabled || event == null) return
        
        try {
            when (event.eventType) {
                AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED,
                AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED -> {
                    handleTextChange(event)
                }
                AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
                    handleWindowStateChange(event)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing accessibility event", e)
        }
    }
    
    private fun handleTextChange(event: AccessibilityEvent) {
        val packageName = event.packageName?.toString() ?: return
        
        // Only process supported chat apps
        if (!isSupportedChatApp(packageName)) return
        
        val rootNode = rootInActiveWindow ?: return
        val extractedMessages = textExtractor.extractChatMessages(rootNode, packageName)
        
        extractedMessages.forEach { message ->
            processChatMessage(message, packageName)
        }
    }
    
    private fun handleWindowStateChange(event: AccessibilityEvent) {
        val packageName = event.packageName?.toString() ?: return
        Log.d(TAG, "Window state changed for: $packageName")
        
        if (isSupportedChatApp(packageName)) {
            // Notify Flutter about app focus change
            PlatformChannelManager.notifyAppFocusChange(packageName, true)
        }
    }
    
    private fun processChatMessage(message: ChatMessage, packageName: String) {
        // Send message to Flutter for translation
        PlatformChannelManager.sendMessageForTranslation(message, packageName)
    }
    
    private fun isSupportedChatApp(packageName: String): Boolean {
        return when (packageName) {
            "com.whatsapp",
            "com.whatsapp.w4b",
            "org.telegram.messenger",
            "com.facebook.orca",
            "com.viber.voip",
            "com.skype.raider",
            "com.snapchat.android",
            "com.instagram.android",
            "com.twitter.android",
            "com.discord" -> true
            else -> false
        }
    }
    
    override fun onInterrupt() {
        Log.d(TAG, "Accessibility service interrupted")
        isServiceEnabled = false
    }
    
    override fun onDestroy() {
        super.onDestroy()
        instance = null
        isServiceEnabled = false
        PlatformChannelManager.notifyAccessibilityServiceStatus(false)
        Log.d(TAG, "TranslatorAccessibilityService destroyed")
    }
    
    // Public methods for external access
    fun findInputField(): AccessibilityNodeInfo? {
        val rootNode = rootInActiveWindow ?: return null
        return textExtractor.findInputField(rootNode)
    }
    
    fun injectText(text: String): Boolean {
        val inputField = findInputField() ?: return false
        return textExtractor.injectTextToField(inputField, text)
    }
}
