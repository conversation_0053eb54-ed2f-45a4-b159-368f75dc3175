import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:translator/translator.dart';
import '../../Config/Cubit/app_cubit.dart';

class TranslationService {
  static final GoogleTranslator _translator = GoogleTranslator();

  static Future<String> translateText(
    String text,
    BuildContext context,
    String languageCode,
  ) async {
    if (text.isEmpty || !context.read<AppCubit>().state.internet) {
      return text;
    }

    // final String currentLang = context.read<SettingsCubit>().state.locale.languageCode;

    try {
      // if (currentLang == 'ar') {
      //   Translation translation = await _translator.translate(text, to: 'ar');
      //   return translation.text;
      // } else if (currentLang == 'en') {
      //   Translation translation = await _translator.translate(text, to: 'en');
      //   return translation.text;
      // }
      Translation translation = await _translator.translate(
        text,
        to: languageCode,
      );
      return translation.text;
    } catch (e) {
      return text;
    }
  }
}

extension StringExtension on String {
  Future<String> translateText(
    BuildContext context,
    String languageCode,
  ) async =>
      await TranslationService.translateText(this, context, languageCode);
}
