import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:meta/meta.dart';

part 'setting_state.dart';

class SettingCubit extends Cubit<SettingState> {
  SettingCubit() : super(SettingState());
  void init() {
    // check if there is a value in the local storage
    // if there is a value, then get it
    // if there is no value, then set it to false
    //---------or------------------------
    // check it all in real permision
  }
  void allowTheAppToTranslateYourRecordings() {
    emit(
      state.copyWith(
        allowTheAppToTranslateYourRecordings:
            !state.allowTheAppToTranslateYourRecordings,
      ),
    );
  }

  void allowTheAppToTranslateTexts() {
    emit(
      state.copyWith(
        allowTheAppToTranslateTexts: !state.allowTheAppToTranslateTexts,
      ),
    );
  }

  void symbolAppearsOnTheScreen() {
    emit(
      state.copyWith(symbolAppearsOnTheScreen: !state.symbolAppearsOnTheScreen),
    );
  }

  void enablePickImage() {
    emit(state.copyWith(enablePickImage: !state.enablePickImage));
  }

  void enablePickFile() {
    emit(state.copyWith(enablePickFile: !state.enablePickFile));
  }

  void enableCamera() {
    emit(state.copyWith(enableCamera: !state.enableCamera));
  }
  void enableMicrophone() {
    emit(state.copyWith(enableMicrophone: !state.enableMicrophone));
  }
}
