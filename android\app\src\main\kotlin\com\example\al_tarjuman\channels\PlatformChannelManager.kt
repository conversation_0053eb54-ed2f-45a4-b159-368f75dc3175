package com.example.al_tarjuman.channels

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import android.util.Log
import io.flutter.plugin.common.MethodChannel
import com.example.al_tarjuman.accessibility.TranslatorAccessibilityService
import com.example.al_tarjuman.models.ChatMessage
import com.example.al_tarjuman.overlay.OverlayService

object PlatformChannelManager {
    
    private const val TAG = "PlatformChannelManager"
    private const val CHANNEL_NAME = "com.example.al_tarjuman/translator"
    
    private var methodChannel: MethodChannel? = null
    private var context: Context? = null
    
    fun initialize(channel: MethodChannel, context: Context) {
        this.methodChannel = channel
        this.context = context
        
        channel.setMethodCallHandler { call, result ->
            when (call.method) {
                "checkOverlayPermission" -> {
                    result.success(checkOverlayPermission())
                }
                "requestOverlayPermission" -> {
                    requestOverlayPermission()
                    result.success(null)
                }
                "checkAccessibilityPermission" -> {
                    result.success(checkAccessibilityPermission())
                }
                "requestAccessibilityPermission" -> {
                    requestAccessibilityPermission()
                    result.success(null)
                }
                "startOverlayService" -> {
                    startOverlayService()
                    result.success(null)
                }
                "stopOverlayService" -> {
                    stopOverlayService()
                    result.success(null)
                }
                "showTranslationOverlay" -> {
                    val messageMap = call.argument<Map<String, Any>>("message")
                    if (messageMap != null) {
                        val message = ChatMessage.fromMap(messageMap)
                        showTranslationOverlay(message)
                        result.success(null)
                    } else {
                        result.error("INVALID_ARGUMENT", "Message data is required", null)
                    }
                }
                "hideTranslationOverlay" -> {
                    val messageId = call.argument<String>("messageId")
                    if (messageId != null) {
                        hideTranslationOverlay(messageId)
                        result.success(null)
                    } else {
                        result.error("INVALID_ARGUMENT", "Message ID is required", null)
                    }
                }
                "injectText" -> {
                    val text = call.argument<String>("text")
                    if (text != null) {
                        val success = injectText(text)
                        result.success(success)
                    } else {
                        result.error("INVALID_ARGUMENT", "Text is required", null)
                    }
                }
                "getInstalledChatApps" -> {
                    val apps = getInstalledChatApps()
                    result.success(apps)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
    
    private fun checkOverlayPermission(): Boolean {
        return context?.let { ctx ->
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                Settings.canDrawOverlays(ctx)
            } else {
                true
            }
        } ?: false
    }
    
    private fun requestOverlayPermission() {
        context?.let { ctx ->
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:${ctx.packageName}")
                ).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                ctx.startActivity(intent)
            }
        }
    }
    
    private fun checkAccessibilityPermission(): Boolean {
        return TranslatorAccessibilityService.instance != null
    }
    
    private fun requestAccessibilityPermission() {
        context?.let { ctx ->
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            ctx.startActivity(intent)
        }
    }
    
    private fun startOverlayService() {
        context?.let { ctx ->
            val intent = Intent(ctx, OverlayService::class.java)
            ctx.startService(intent)
        }
    }
    
    private fun stopOverlayService() {
        context?.let { ctx ->
            val intent = Intent(ctx, OverlayService::class.java)
            ctx.stopService(intent)
        }
    }
    
    private fun showTranslationOverlay(message: ChatMessage) {
        OverlayService.instance?.showTranslationOverlay(message)
    }
    
    private fun hideTranslationOverlay(messageId: String) {
        OverlayService.instance?.removeOverlay(messageId)
    }
    
    private fun injectText(text: String): Boolean {
        return TranslatorAccessibilityService.instance?.injectText(text) ?: false
    }
    
    private fun getInstalledChatApps(): List<Map<String, String>> {
        val chatApps = listOf(
            mapOf("package" to "com.whatsapp", "name" to "WhatsApp"),
            mapOf("package" to "com.whatsapp.w4b", "name" to "WhatsApp Business"),
            mapOf("package" to "org.telegram.messenger", "name" to "Telegram"),
            mapOf("package" to "com.facebook.orca", "name" to "Messenger"),
            mapOf("package" to "com.viber.voip", "name" to "Viber"),
            mapOf("package" to "com.skype.raider", "name" to "Skype"),
            mapOf("package" to "com.snapchat.android", "name" to "Snapchat"),
            mapOf("package" to "com.instagram.android", "name" to "Instagram"),
            mapOf("package" to "com.twitter.android", "name" to "Twitter"),
            mapOf("package" to "com.discord", "name" to "Discord")
        )
        
        return context?.let { ctx ->
            val packageManager = ctx.packageManager
            chatApps.filter { app ->
                try {
                    packageManager.getPackageInfo(app["package"]!!, 0)
                    true
                } catch (e: Exception) {
                    false
                }
            }
        } ?: emptyList()
    }
    
    // Methods to send data from Android to Flutter
    fun sendMessageForTranslation(message: ChatMessage, packageName: String) {
        methodChannel?.invokeMethod("onMessageDetected", mapOf(
            "message" to message.toMap(),
            "packageName" to packageName
        ))
    }
    
    fun notifyAccessibilityServiceStatus(isEnabled: Boolean) {
        methodChannel?.invokeMethod("onAccessibilityServiceStatusChanged", mapOf(
            "isEnabled" to isEnabled
        ))
    }
    
    fun notifyAppFocusChange(packageName: String, isFocused: Boolean) {
        methodChannel?.invokeMethod("onAppFocusChanged", mapOf(
            "packageName" to packageName,
            "isFocused" to isFocused
        ))
    }
    
    fun notifyTranslationDisplayed(messageId: String) {
        methodChannel?.invokeMethod("onTranslationDisplayed", mapOf(
            "messageId" to messageId
        ))
    }
}
