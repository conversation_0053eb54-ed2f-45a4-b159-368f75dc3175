package com.example.al_tarjuman.channels

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import android.util.Log
import com.example.al_tarjuman.accessibility.TranslatorAccessibilityService
import com.example.al_tarjuman.accessibility.AccessibilityServiceTester
import com.example.al_tarjuman.models.ChatMessage
import com.example.al_tarjuman.overlay.OverlayService
import com.example.al_tarjuman.overlay.OverlayManager
import com.example.al_tarjuman.injection.TextInjectionCoordinator
import com.example.al_tarjuman.injection.TextInjectionManager

object PlatformChannelManager {
    
    private const val TAG = "PlatformChannelManager"
    private const val CHANNEL_NAME = "com.example.al_tarjuman/translator"
    
    private var methodChannel: MethodChannel? = null
    private var context: Context? = null
    
    fun initialize(channel: MethodChannel, context: Context) {
        this.methodChannel = channel
        this.context = context
        
        channel.setMethodCallHandler { call, result ->
            when (call.method) {
                "checkOverlayPermission" -> {
                    result.success(checkOverlayPermission())
                }
                "requestOverlayPermission" -> {
                    requestOverlayPermission()
                    result.success(null)
                }
                "checkAccessibilityPermission" -> {
                    result.success(checkAccessibilityPermission())
                }
                "requestAccessibilityPermission" -> {
                    requestAccessibilityPermission()
                    result.success(null)
                }
                "startOverlayService" -> {
                    startOverlayService()
                    result.success(null)
                }
                "stopOverlayService" -> {
                    stopOverlayService()
                    result.success(null)
                }
                "showTranslationOverlay" -> {
                    val messageMap = call.argument<Map<String, Any>>("message")
                    if (messageMap != null) {
                        val message = ChatMessage.fromMap(messageMap)
                        showTranslationOverlay(message)
                        result.success(null)
                    } else {
                        result.error("INVALID_ARGUMENT", "Message data is required", null)
                    }
                }
                "hideTranslationOverlay" -> {
                    val messageId = call.argument<String>("messageId")
                    if (messageId != null) {
                        hideTranslationOverlay(messageId)
                        result.success(null)
                    } else {
                        result.error("INVALID_ARGUMENT", "Message ID is required", null)
                    }
                }
                "injectText" -> {
                    val text = call.argument<String>("text")
                    if (text != null) {
                        val success = injectText(text)
                        result.success(success)
                    } else {
                        result.error("INVALID_ARGUMENT", "Text is required", null)
                    }
                }
                "getInstalledChatApps" -> {
                    val apps = getInstalledChatApps()
                    result.success(apps)
                }
                "runAccessibilityTests" -> {
                    val testResults = runAccessibilityTests()
                    result.success(testResults)
                }
                "getAccessibilityServiceStatus" -> {
                    val status = getAccessibilityServiceStatus()
                    result.success(status)
                }
                "getOverlayStatistics" -> {
                    val stats = getOverlayStatistics()
                    result.success(stats)
                }
                "configureOverlays" -> {
                    configureOverlays(call.arguments as? Map<String, Any>)
                    result.success(null)
                }
                "removeAllOverlays" -> {
                    val animate = call.argument<Boolean>("animate") ?: true
                    removeAllOverlays(animate)
                    result.success(null)
                }
                "hideOverlaysTemporarily" -> {
                    val durationMs = call.argument<Int>("durationMs") ?: 3000
                    hideOverlaysTemporarily(durationMs.toLong())
                    result.success(null)
                }
                "repositionAllOverlays" -> {
                    repositionAllOverlays()
                    result.success(null)
                }
                "getOverlayDetails" -> {
                    val details = getOverlayDetails()
                    result.success(details)
                }
                "injectText" -> {
                    val text = call.argument<String>("text") ?: ""
                    val targetPackage = call.argument<String>("targetPackage") ?: ""
                    val strategy = call.argument<String>("strategy") ?: "SMART"
                    val timeout = call.argument<Int>("timeout") ?: 10000
                    val enableRetries = call.argument<Boolean>("enableRetries") ?: true
                    val enableFallbacks = call.argument<Boolean>("enableFallbacks") ?: true
                    val maxRetries = call.argument<Int>("maxRetries") ?: 3

                    injectText(text, targetPackage, strategy, timeout.toLong(), enableRetries, enableFallbacks, maxRetries) { injectionResult ->
                        result.success(injectionResult)
                    }
                }
                "canInjectText" -> {
                    val canInject = canInjectText()
                    result.success(canInject)
                }
                "getCurrentInputFieldInfo" -> {
                    val info = getCurrentInputFieldInfo()
                    result.success(info)
                }
                "getInjectionStatistics" -> {
                    val stats = getInjectionStatistics()
                    result.success(stats)
                }
                "getRecommendedStrategy" -> {
                    val packageName = call.argument<String>("packageName") ?: ""
                    val strategy = getRecommendedStrategy(packageName)
                    result.success(strategy)
                }
                "configure" -> {
                    val preferredStrategy = call.argument<String>("preferredStrategy")
                    val enableRetries = call.argument<Boolean>("enableRetries")
                    val enableFallbacks = call.argument<Boolean>("enableFallbacks")
                    val maxRetries = call.argument<Int>("maxRetries")
                    val timeout = call.argument<Int>("timeout")

                    configureTextInjection(preferredStrategy, enableRetries, enableFallbacks, maxRetries, timeout?.toLong())
                    result.success(null)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
    
    private fun checkOverlayPermission(): Boolean {
        return context?.let { ctx ->
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                Settings.canDrawOverlays(ctx)
            } else {
                true
            }
        } ?: false
    }
    
    private fun requestOverlayPermission() {
        context?.let { ctx ->
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:${ctx.packageName}")
                ).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                ctx.startActivity(intent)
            }
        }
    }
    
    private fun checkAccessibilityPermission(): Boolean {
        return TranslatorAccessibilityService.instance != null
    }
    
    private fun requestAccessibilityPermission() {
        context?.let { ctx ->
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            ctx.startActivity(intent)
        }
    }
    
    private fun startOverlayService() {
        context?.let { ctx ->
            val intent = Intent(ctx, OverlayService::class.java)
            ctx.startService(intent)
        }
    }
    
    private fun stopOverlayService() {
        context?.let { ctx ->
            val intent = Intent(ctx, OverlayService::class.java)
            ctx.stopService(intent)
        }
    }
    
    private fun showTranslationOverlay(message: ChatMessage) {
        OverlayService.instance?.showTranslationOverlay(message)
    }
    
    private fun hideTranslationOverlay(messageId: String) {
        OverlayService.instance?.removeOverlay(messageId)
    }
    
    private fun injectText(text: String): Boolean {
        return TranslatorAccessibilityService.instance?.injectText(text) ?: false
    }
    
    private fun getInstalledChatApps(): List<Map<String, String>> {
        val chatApps = listOf(
            mapOf("package" to "com.whatsapp", "name" to "WhatsApp"),
            mapOf("package" to "com.whatsapp.w4b", "name" to "WhatsApp Business"),
            mapOf("package" to "org.telegram.messenger", "name" to "Telegram"),
            mapOf("package" to "com.facebook.orca", "name" to "Messenger"),
            mapOf("package" to "com.viber.voip", "name" to "Viber"),
            mapOf("package" to "com.skype.raider", "name" to "Skype"),
            mapOf("package" to "com.snapchat.android", "name" to "Snapchat"),
            mapOf("package" to "com.instagram.android", "name" to "Instagram"),
            mapOf("package" to "com.twitter.android", "name" to "Twitter"),
            mapOf("package" to "com.discord", "name" to "Discord")
        )
        
        return context?.let { ctx ->
            val packageManager = ctx.packageManager
            chatApps.filter { app ->
                try {
                    packageManager.getPackageInfo(app["package"]!!, 0)
                    true
                } catch (e: Exception) {
                    false
                }
            }
        } ?: emptyList()
    }
    
    // Methods to send data from Android to Flutter
    fun sendMessageForTranslation(message: ChatMessage, packageName: String) {
        methodChannel?.invokeMethod("onMessageDetected", mapOf(
            "message" to message.toMap(),
            "packageName" to packageName
        ))
    }
    
    fun notifyAccessibilityServiceStatus(isEnabled: Boolean) {
        methodChannel?.invokeMethod("onAccessibilityServiceStatusChanged", mapOf(
            "isEnabled" to isEnabled
        ))
    }
    
    fun notifyAppFocusChange(packageName: String, isFocused: Boolean) {
        methodChannel?.invokeMethod("onAppFocusChanged", mapOf(
            "packageName" to packageName,
            "isFocused" to isFocused
        ))
    }
    
    fun notifyTranslationDisplayed(messageId: String) {
        methodChannel?.invokeMethod("onTranslationDisplayed", mapOf(
            "messageId" to messageId
        ))
    }

    // Testing and debugging methods
    private fun runAccessibilityTests(): Map<String, Any> {
        return context?.let { ctx ->
            try {
                val tester = AccessibilityServiceTester(ctx)
                val results = tester.runAllTests()

                mapOf(
                    "success" to true,
                    "report" to tester.generateTestReport(),
                    "results" to results.map { result ->
                        mapOf(
                            "testName" to result.testName,
                            "passed" to result.passed,
                            "total" to result.total,
                            "isSuccess" to result.isSuccess,
                            "successRate" to result.successRate,
                            "details" to result.details
                        )
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "Error running accessibility tests", e)
                mapOf(
                    "success" to false,
                    "error" to e.message,
                    "report" to "Test execution failed: ${e.message}"
                )
            }
        } ?: mapOf(
            "success" to false,
            "error" to "Context not available"
        )
    }

    private fun getAccessibilityServiceStatus(): Map<String, Any> {
        return try {
            val serviceInstance = TranslatorAccessibilityService.instance
            if (serviceInstance != null) {
                serviceInstance.getServiceStatus()
            } else {
                mapOf(
                    "isEnabled" to false,
                    "error" to "Service instance not available"
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting accessibility service status", e)
            mapOf(
                "isEnabled" to false,
                "error" to e.message
            )
        }
    }

    // Overlay management methods
    private fun getOverlayStatistics(): Map<String, Any> {
        return context?.let { ctx ->
            try {
                val overlayManager = OverlayManager.getInstance(ctx)
                overlayManager.getOverlayStatistics()
            } catch (e: Exception) {
                Log.e(TAG, "Error getting overlay statistics", e)
                mapOf("error" to e.message)
            }
        } ?: mapOf("error" to "Context not available")
    }

    private fun configureOverlays(arguments: Map<String, Any>?) {
        context?.let { ctx ->
            try {
                val overlayManager = OverlayManager.getInstance(ctx)
                val service = OverlayService.instance

                arguments?.let { args ->
                    // Configure overlay style
                    (args["style"] as? String)?.let { style ->
                        val overlayStyle = when (style) {
                            "BUBBLE" -> OverlayService.OverlayStyle.BUBBLE
                            "CARD" -> OverlayService.OverlayStyle.CARD
                            "MINIMAL" -> OverlayService.OverlayStyle.MINIMAL
                            "FLOATING" -> OverlayService.OverlayStyle.FLOATING
                            else -> OverlayService.OverlayStyle.BUBBLE
                        }
                        service?.setOverlayStyle(overlayStyle)
                    }

                    // Configure overlay position
                    (args["position"] as? String)?.let { position ->
                        val overlayPosition = when (position) {
                            "SMART" -> OverlayService.OverlayPosition.SMART
                            "TOP" -> OverlayService.OverlayPosition.TOP
                            "BOTTOM" -> OverlayService.OverlayPosition.BOTTOM
                            "CENTER" -> OverlayService.OverlayPosition.CENTER
                            "FOLLOW" -> OverlayService.OverlayPosition.FOLLOW
                            else -> OverlayService.OverlayPosition.SMART
                        }
                        service?.setOverlayPosition(overlayPosition)
                    }

                    // Configure other settings
                    (args["animationsEnabled"] as? Boolean)?.let { enabled ->
                        service?.setAnimationsEnabled(enabled)
                    }

                    (args["autoHideEnabled"] as? Boolean)?.let { enabled ->
                        service?.setAutoHideEnabled(enabled)
                    }

                    (args["opacity"] as? Double)?.let { opacity ->
                        service?.setOverlayOpacity(opacity.toFloat())
                    }

                    // Configure manager settings
                    overlayManager.configureOverlays(
                        maxConcurrent = args["maxConcurrent"] as? Int,
                        displayDuration = (args["displayDuration"] as? Int)?.toLong()
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error configuring overlays", e)
            }
        }
    }

    private fun removeAllOverlays(animate: Boolean) {
        context?.let { ctx ->
            try {
                val overlayManager = OverlayManager.getInstance(ctx)
                overlayManager.removeAllOverlays(animate)
            } catch (e: Exception) {
                Log.e(TAG, "Error removing all overlays", e)
            }
        }
    }

    private fun hideOverlaysTemporarily(durationMs: Long) {
        context?.let { ctx ->
            try {
                val overlayManager = OverlayManager.getInstance(ctx)
                overlayManager.hideOverlaysTemporarily(durationMs)
            } catch (e: Exception) {
                Log.e(TAG, "Error hiding overlays temporarily", e)
            }
        }
    }

    private fun repositionAllOverlays() {
        context?.let { ctx ->
            try {
                val overlayManager = OverlayManager.getInstance(ctx)
                overlayManager.repositionAllOverlays()
            } catch (e: Exception) {
                Log.e(TAG, "Error repositioning overlays", e)
            }
        }
    }

    private fun getOverlayDetails(): List<Map<String, Any>> {
        return context?.let { ctx ->
            try {
                val overlayManager = OverlayManager.getInstance(ctx)
                overlayManager.getOverlayDetails()
            } catch (e: Exception) {
                Log.e(TAG, "Error getting overlay details", e)
                emptyList()
            }
        } ?: emptyList()
    }

    // Text injection methods
    private fun injectText(
        text: String,
        targetPackage: String,
        strategy: String,
        timeout: Long,
        enableRetries: Boolean,
        enableFallbacks: Boolean,
        maxRetries: Int,
        callback: (Map<String, Any>) -> Unit
    ) {
        context?.let { ctx ->
            try {
                val coordinator = TextInjectionCoordinator(ctx)
                val accessibilityService = TranslatorAccessibilityService.instance

                val injectionStrategy = when (strategy.uppercase()) {
                    "SET_TEXT" -> TextInjectionManager.InjectionStrategy.SET_TEXT
                    "CLIPBOARD_PASTE" -> TextInjectionManager.InjectionStrategy.CLIPBOARD_PASTE
                    "SIMULATE_TYPING" -> TextInjectionManager.InjectionStrategy.SIMULATE_TYPING
                    "GESTURE_BASED" -> TextInjectionManager.InjectionStrategy.GESTURE_BASED
                    "HYBRID" -> TextInjectionManager.InjectionStrategy.HYBRID
                    else -> TextInjectionManager.InjectionStrategy.SMART
                }

                val injectionContext = TextInjectionCoordinator.InjectionContext(
                    text = text,
                    targetPackage = targetPackage,
                    accessibilityService = accessibilityService,
                    inputField = null, // Will be found automatically
                    preferredStrategy = injectionStrategy,
                    timeout = timeout,
                    maxRetries = maxRetries
                )

                coordinator.injectText(injectionContext) { success, error ->
                    val result = mapOf(
                        "requestId" to "req_${System.currentTimeMillis()}",
                        "success" to success,
                        "strategy" to strategy,
                        "duration" to 0, // Would need to track actual duration
                        "error" to error,
                        "retryCount" to 0,
                        "timestamp" to System.currentTimeMillis(),
                        "targetPackage" to targetPackage
                    )
                    callback(result)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error injecting text", e)
                val errorResult = mapOf(
                    "requestId" to "error_${System.currentTimeMillis()}",
                    "success" to false,
                    "strategy" to strategy,
                    "duration" to 0,
                    "error" to e.message,
                    "retryCount" to 0,
                    "timestamp" to System.currentTimeMillis(),
                    "targetPackage" to targetPackage
                )
                callback(errorResult)
            }
        }
    }

    private fun canInjectText(): Boolean {
        return context?.let { ctx ->
            try {
                val accessibilityService = TranslatorAccessibilityService.instance
                accessibilityService != null
            } catch (e: Exception) {
                Log.e(TAG, "Error checking injection capability", e)
                false
            }
        } ?: false
    }

    private fun getCurrentInputFieldInfo(): Map<String, Any?> {
        return context?.let { ctx ->
            try {
                val accessibilityService = TranslatorAccessibilityService.instance
                if (accessibilityService != null) {
                    val rootNode = accessibilityService.rootInActiveWindow
                    if (rootNode != null) {
                        // Find input field and get its info
                        val inputField = findInputField(rootNode)
                        mapOf(
                            "hasInputField" to (inputField != null),
                            "packageName" to rootNode.packageName?.toString(),
                            "hint" to inputField?.hintText?.toString(),
                            "currentText" to inputField?.text?.toString(),
                            "isPasswordField" to (inputField?.isPassword == true),
                            "isMultiline" to false, // Would need to check input type
                            "capabilities" to mapOf(
                                "canSetText" to true,
                                "canPaste" to true,
                                "canType" to true
                            )
                        )
                    } else {
                        mapOf("hasInputField" to false)
                    }
                } else {
                    mapOf("hasInputField" to false, "error" to "AccessibilityService not available")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error getting input field info", e)
                mapOf("hasInputField" to false, "error" to e.message)
            }
        } ?: mapOf("hasInputField" to false, "error" to "Context not available")
    }

    private fun getInjectionStatistics(): Map<String, Any> {
        return context?.let { ctx ->
            try {
                val coordinator = TextInjectionCoordinator(ctx)
                coordinator.getStatistics()
            } catch (e: Exception) {
                Log.e(TAG, "Error getting injection statistics", e)
                mapOf("error" to e.message)
            }
        } ?: mapOf("error" to "Context not available")
    }

    private fun getRecommendedStrategy(packageName: String): String {
        return context?.let { ctx ->
            try {
                val coordinator = TextInjectionCoordinator(ctx)
                coordinator.getRecommendedStrategy(packageName).name
            } catch (e: Exception) {
                Log.e(TAG, "Error getting recommended strategy", e)
                "SMART"
            }
        } ?: "SMART"
    }

    private fun configureTextInjection(
        preferredStrategy: String?,
        enableRetries: Boolean?,
        enableFallbacks: Boolean?,
        maxRetries: Int?,
        timeout: Long?
    ) {
        // Configuration would be stored and used by the injection coordinator
        Log.d(TAG, "Text injection configured: strategy=$preferredStrategy, retries=$enableRetries")
    }

    private fun findInputField(rootNode: android.view.accessibility.AccessibilityNodeInfo): android.view.accessibility.AccessibilityNodeInfo? {
        // Simple implementation - in real scenario, would use TextInjectionManager's logic
        if (rootNode.className?.toString() == "android.widget.EditText" &&
            rootNode.isEditable && rootNode.isFocusable) {
            return rootNode
        }

        for (i in 0 until rootNode.childCount) {
            rootNode.getChild(i)?.let { child ->
                val found = findInputField(child)
                if (found != null) return found
            }
        }

        return null
    }
}
