// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a tr locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'tr';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts": MessageLookupByLibrary.simpleMessage(
            "Çevirmen\'in yapay zeka kullanarak metinleri otomatik olarak çevirmesine ve seçilen dilde göndermesine izin verin."),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "Çevirmen\'in yapay zeka kullanarak ses kayıtlarınızı otomatik olarak çevirmesine ve seçilen dilde göndermesine izin verin."),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "Telefon uygulamalarına erişime izin verin."),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "Çevirmen ile anında ve akıcı çeviri deneyiminin keyfini çıkarın, metinleri, kelimeleri ve ses kayıtlarını tercih ettiğiniz dile kolayca ve hızlıca dönüştürebilirsiniz. Yüksek doğruluk ve rahat deneyim sağlayan gelişmiş yapay zeka teknolojileri sayesinde."),
        "enable_camera": MessageLookupByLibrary.simpleMessage(
            "Çevirmen\'in kamerayı kullanmasına izin ver"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage(
            "Çevirmen\'in mikrofonu kullanmasına izin ver"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "Çevirmen\'in galeriden dosya içe aktarmasına izin ver"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "Çevirmen\'in galeriden resim içe aktarmasına izin ver"),
        "language": MessageLookupByLibrary.simpleMessage("Dil"),
        "live_translation":
            MessageLookupByLibrary.simpleMessage("Çeviriyi buraya yazın..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "Yapay zeka kullanarak ses kayıtlarını çevrilmiş kayda dönüştürün."),
        "name_app": MessageLookupByLibrary.simpleMessage("Çevirmen"),
        "search_language": MessageLookupByLibrary.simpleMessage("Dil ara..."),
        "symbol_appears_on_the_screen":
            MessageLookupByLibrary.simpleMessage("Sembol ekranda görünür"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "Yapay zekanın gücü sayesinde Çevirmen ile anında çevirin."),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "Metinleri şimdi çevirmek için Çevirmen\'i kullanın.")
      };
}
