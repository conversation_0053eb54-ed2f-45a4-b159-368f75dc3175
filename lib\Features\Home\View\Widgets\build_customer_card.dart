import '../../../../Core/Resources/text_style.dart';
import '../../../../Core/Utils/Enum/type_cards.dart';
import '../../../../Core/Utils/Extensions/localizations_extension.dart';
import '../../../../Core/Utils/Widget/build_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildCustomerCard extends StatelessWidget {
  const BuildCustomerCard({
    super.key,
    required this.title,
    required this.typeCards,
  });
  final String title;
  final TypeCards typeCards;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: typeCards.height,
      width: typeCards.width,
      decoration: BoxDecoration(
        color: Color(typeCards.valueColors),
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: Color(typeCards.valueColors).withAl<PERSON>(50),
            spreadRadius: 0,
            blurRadius: 10,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Stack(
        children: [
          Align(
            alignment: context.isRtl ? Alignment.topRight : Alignment.topLeft,
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Text(
                title,
                maxLines: title.length,
                style: AppTextStyles.h7Bold.copyWith(color: Colors.white),
              ),
            ),
          ),
          
          Align(
            alignment: context.isRtl
                ? Alignment.bottomLeft
                : Alignment.bottomRight,
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: BuildImageAssets(svg: typeCards.image, width: typeCards.widthImage.w),
            ),
          ),
        ],
      ),
    );
  }
}
