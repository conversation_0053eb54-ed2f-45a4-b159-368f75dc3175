import '../../Core/Storage/Models/language_mode.dart';
import 'package:flutter/material.dart';

@immutable
class AppState {
  final ThemeMode themeMode;
  final Locale locale;
  final List<LanguageMode> locales;
  final bool internet;
  final bool loading;
  final double logoOpacity;

  final double textOpacity;

  final double loadingOpacity;

  const AppState({
    this.themeMode = ThemeMode.light,
    required this.locale,
    this.internet = false,
    this.loading = true,
    this.loadingOpacity = 0.0,
    this.logoOpacity = 0.0,
    this.locales = const [],
    this.textOpacity = 0.0,
  });

  AppState copyWith({
    ThemeMode? themeMode,
    Locale? locale,
    bool? internet,
    bool? loading,
    double? logoOpacity,
    List<LanguageMode>? locales,
    double? textOpacity,

    double? loadingOpacity,
  }) {
    return AppState(
      themeMode: themeMode ?? this.themeMode,
      locale: locale ?? this.locale,
      internet: internet ?? this.internet,
      loading: loading ?? this.loading,
      logoOpacity: logoOpacity ?? this.logoOpacity,
      textOpacity: textOpacity ?? this.textOpacity,
      loadingOpacity: loadingOpacity ?? this.loadingOpacity,
      locales: locales ?? this.locales,
    );
  }
}
