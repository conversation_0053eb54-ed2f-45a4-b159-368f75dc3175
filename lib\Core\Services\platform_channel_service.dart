import 'dart:async';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

class PlatformChannelService {
  static const MethodChannel _channel = MethodChannel(
    'com.example.al_tarjuman/translator',
  );

  static PlatformChannelService? _instance;
  static PlatformChannelService get instance =>
      _instance ??= PlatformChannelService._();

  PlatformChannelService._() {
    _setupMethodCallHandler();
  }

  // Stream controllers for events from Android
  final StreamController<Map<String, dynamic>> _messageDetectedController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<bool> _accessibilityStatusController =
      StreamController<bool>.broadcast();
  final StreamController<Map<String, dynamic>> _appFocusController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<String> _translationDisplayedController =
      StreamController<String>.broadcast();

  // Public streams
  Stream<Map<String, dynamic>> get onMessageDetected =>
      _messageDetectedController.stream;
  Stream<bool> get onAccessibilityStatusChanged =>
      _accessibilityStatusController.stream;
  Stream<Map<String, dynamic>> get onAppFocusChanged =>
      _appFocusController.stream;
  Stream<String> get onTranslationDisplayed =>
      _translationDisplayedController.stream;

  void _setupMethodCallHandler() {
    _channel.setMethodCallHandler((MethodCall call) async {
      try {
        switch (call.method) {
          case 'onMessageDetected':
            final data = Map<String, dynamic>.from(call.arguments);
            _messageDetectedController.add(data);
            break;
          case 'onAccessibilityServiceStatusChanged':
            final isEnabled = call.arguments['isEnabled'] as bool;
            _accessibilityStatusController.add(isEnabled);
            break;
          case 'onAppFocusChanged':
            final data = Map<String, dynamic>.from(call.arguments);
            _appFocusController.add(data);
            break;
          case 'onTranslationDisplayed':
            final messageId = call.arguments['messageId'] as String;
            _translationDisplayedController.add(messageId);
            break;
          default:
            debugPrint('Unknown method call: ${call.method}');
        }
      } catch (e) {
        debugPrint('Error handling method call: $e');
      }
    });
  }

  // Permission methods
  Future<bool> checkOverlayPermission() async {
    try {
      final result = await _channel.invokeMethod<bool>(
        'checkOverlayPermission',
      );
      return result ?? false;
    } catch (e) {
      debugPrint('Error checking overlay permission: $e');
      return false;
    }
  }

  Future<void> requestOverlayPermission() async {
    try {
      await _channel.invokeMethod('requestOverlayPermission');
    } catch (e) {
      debugPrint('Error requesting overlay permission: $e');
    }
  }

  Future<bool> checkAccessibilityPermission() async {
    try {
      final result = await _channel.invokeMethod<bool>(
        'checkAccessibilityPermission',
      );
      return result ?? false;
    } catch (e) {
      debugPrint('Error checking accessibility permission: $e');
      return false;
    }
  }

  Future<void> requestAccessibilityPermission() async {
    try {
      await _channel.invokeMethod('requestAccessibilityPermission');
    } catch (e) {
      debugPrint('Error requesting accessibility permission: $e');
    }
  }

  // Service control methods
  Future<void> startOverlayService() async {
    try {
      await _channel.invokeMethod('startOverlayService');
    } catch (e) {
      debugPrint('Error starting overlay service: $e');
    }
  }

  Future<void> stopOverlayService() async {
    try {
      await _channel.invokeMethod('stopOverlayService');
    } catch (e) {
      debugPrint('Error stopping overlay service: $e');
    }
  }

  // Translation overlay methods
  Future<void> showTranslationOverlay(Map<String, dynamic> message) async {
    try {
      await _channel.invokeMethod('showTranslationOverlay', {
        'message': message,
      });
    } catch (e) {
      debugPrint('Error showing translation overlay: $e');
    }
  }

  Future<void> hideTranslationOverlay(String messageId) async {
    try {
      await _channel.invokeMethod('hideTranslationOverlay', {
        'messageId': messageId,
      });
    } catch (e) {
      debugPrint('Error hiding translation overlay: $e');
    }
  }

  // Text injection method (legacy - for backward compatibility)
  Future<bool> injectText(String text, {String? targetPackage}) async {
    try {
      final result = await injectTextAdvanced(
        text: text,
        targetPackage: targetPackage ?? 'unknown',
      );
      return result['success'] as bool? ?? false;
    } catch (e) {
      debugPrint('Error injecting text: $e');
      return false;
    }
  }

  // Get installed chat apps
  Future<List<Map<String, String>>> getInstalledChatApps() async {
    try {
      final result = await _channel.invokeMethod<List>('getInstalledChatApps');
      return result?.cast<Map<String, String>>() ?? [];
    } catch (e) {
      debugPrint('Error getting installed chat apps: $e');
      return [];
    }
  }

  // Testing and debugging methods
  Future<Map<String, dynamic>> runAccessibilityTests() async {
    try {
      final result = await _channel.invokeMethod<Map>('runAccessibilityTests');
      return Map<String, dynamic>.from(result ?? {});
    } catch (e) {
      debugPrint('Error running accessibility tests: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>> getAccessibilityServiceStatus() async {
    try {
      final result = await _channel.invokeMethod<Map>(
        'getAccessibilityServiceStatus',
      );
      return Map<String, dynamic>.from(result ?? {});
    } catch (e) {
      debugPrint('Error getting accessibility service status: $e');
      return {'isEnabled': false, 'error': e.toString()};
    }
  }

  // Overlay management methods
  Future<Map<String, dynamic>> getOverlayStatistics() async {
    try {
      final result = await _channel.invokeMethod<Map>('getOverlayStatistics');
      return Map<String, dynamic>.from(result ?? {});
    } catch (e) {
      debugPrint('Error getting overlay statistics: $e');
      return {};
    }
  }

  Future<void> configureOverlays({
    String? style,
    String? position,
    bool? animationsEnabled,
    bool? autoHideEnabled,
    double? opacity,
    int? maxConcurrent,
    int? displayDuration,
  }) async {
    try {
      await _channel.invokeMethod('configureOverlays', {
        if (style != null) 'style': style,
        if (position != null) 'position': position,
        if (animationsEnabled != null) 'animationsEnabled': animationsEnabled,
        if (autoHideEnabled != null) 'autoHideEnabled': autoHideEnabled,
        if (opacity != null) 'opacity': opacity,
        if (maxConcurrent != null) 'maxConcurrent': maxConcurrent,
        if (displayDuration != null) 'displayDuration': displayDuration,
      });
    } catch (e) {
      debugPrint('Error configuring overlays: $e');
    }
  }

  Future<void> removeAllOverlays({bool animate = true}) async {
    try {
      await _channel.invokeMethod('removeAllOverlays', {'animate': animate});
    } catch (e) {
      debugPrint('Error removing all overlays: $e');
    }
  }

  Future<void> hideOverlaysTemporarily({int durationMs = 3000}) async {
    try {
      await _channel.invokeMethod('hideOverlaysTemporarily', {
        'durationMs': durationMs,
      });
    } catch (e) {
      debugPrint('Error hiding overlays temporarily: $e');
    }
  }

  Future<void> repositionAllOverlays() async {
    try {
      await _channel.invokeMethod('repositionAllOverlays');
    } catch (e) {
      debugPrint('Error repositioning overlays: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getOverlayDetails() async {
    try {
      final result = await _channel.invokeMethod<List>('getOverlayDetails');
      return result?.cast<Map<String, dynamic>>() ?? [];
    } catch (e) {
      debugPrint('Error getting overlay details: $e');
      return [];
    }
  }

  // Advanced text injection methods
  Future<Map<String, dynamic>> injectTextAdvanced({
    required String text,
    required String targetPackage,
    String strategy = 'SMART',
    int timeout = 10000,
    bool enableRetries = true,
    bool enableFallbacks = true,
    int maxRetries = 3,
  }) async {
    try {
      final result = await _channel.invokeMethod<Map>('injectText', {
        'text': text,
        'targetPackage': targetPackage,
        'strategy': strategy,
        'timeout': timeout,
        'enableRetries': enableRetries,
        'enableFallbacks': enableFallbacks,
        'maxRetries': maxRetries,
      });
      return Map<String, dynamic>.from(result ?? {});
    } catch (e) {
      debugPrint('Error injecting text: $e');
      return {
        'success': false,
        'error': e.toString(),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
    }
  }

  Future<bool> canInjectText() async {
    try {
      final result = await _channel.invokeMethod<bool>('canInjectText');
      return result ?? false;
    } catch (e) {
      debugPrint('Error checking injection capability: $e');
      return false;
    }
  }

  Future<Map<String, dynamic>> getCurrentInputFieldInfo() async {
    try {
      final result = await _channel.invokeMethod<Map>(
        'getCurrentInputFieldInfo',
      );
      return Map<String, dynamic>.from(result ?? {});
    } catch (e) {
      debugPrint('Error getting input field info: $e');
      return {'hasInputField': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>> getInjectionStatistics() async {
    try {
      final result = await _channel.invokeMethod<Map>('getInjectionStatistics');
      return Map<String, dynamic>.from(result ?? {});
    } catch (e) {
      debugPrint('Error getting injection statistics: $e');
      return {};
    }
  }

  Future<String> getRecommendedStrategy(String packageName) async {
    try {
      final result = await _channel.invokeMethod<String>(
        'getRecommendedStrategy',
        {'packageName': packageName},
      );
      return result ?? 'SMART';
    } catch (e) {
      debugPrint('Error getting recommended strategy: $e');
      return 'SMART';
    }
  }

  Future<void> configureTextInjection({
    String? preferredStrategy,
    bool? enableRetries,
    bool? enableFallbacks,
    int? maxRetries,
    int? timeout,
  }) async {
    try {
      await _channel.invokeMethod('configure', {
        if (preferredStrategy != null) 'preferredStrategy': preferredStrategy,
        if (enableRetries != null) 'enableRetries': enableRetries,
        if (enableFallbacks != null) 'enableFallbacks': enableFallbacks,
        if (maxRetries != null) 'maxRetries': maxRetries,
        if (timeout != null) 'timeout': timeout,
      });
    } catch (e) {
      debugPrint('Error configuring text injection: $e');
    }
  }

  void dispose() {
    _messageDetectedController.close();
    _accessibilityStatusController.close();
    _appFocusController.close();
    _translationDisplayedController.close();
  }
}
