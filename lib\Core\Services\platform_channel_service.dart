import 'dart:async';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

class PlatformChannelService {
  static const MethodChannel _channel = MethodChannel('com.example.al_tarjuman/translator');
  
  static PlatformChannelService? _instance;
  static PlatformChannelService get instance => _instance ??= PlatformChannelService._();
  
  PlatformChannelService._() {
    _setupMethodCallHandler();
  }
  
  // Stream controllers for events from Android
  final StreamController<Map<String, dynamic>> _messageDetectedController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<bool> _accessibilityStatusController = 
      StreamController<bool>.broadcast();
  final StreamController<Map<String, dynamic>> _appFocusController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<String> _translationDisplayedController = 
      StreamController<String>.broadcast();
  
  // Public streams
  Stream<Map<String, dynamic>> get onMessageDetected => _messageDetectedController.stream;
  Stream<bool> get onAccessibilityStatusChanged => _accessibilityStatusController.stream;
  Stream<Map<String, dynamic>> get onAppFocusChanged => _appFocusController.stream;
  Stream<String> get onTranslationDisplayed => _translationDisplayedController.stream;
  
  void _setupMethodCallHandler() {
    _channel.setMethodCallHandler((MethodCall call) async {
      try {
        switch (call.method) {
          case 'onMessageDetected':
            final data = Map<String, dynamic>.from(call.arguments);
            _messageDetectedController.add(data);
            break;
          case 'onAccessibilityServiceStatusChanged':
            final isEnabled = call.arguments['isEnabled'] as bool;
            _accessibilityStatusController.add(isEnabled);
            break;
          case 'onAppFocusChanged':
            final data = Map<String, dynamic>.from(call.arguments);
            _appFocusController.add(data);
            break;
          case 'onTranslationDisplayed':
            final messageId = call.arguments['messageId'] as String;
            _translationDisplayedController.add(messageId);
            break;
          default:
            debugPrint('Unknown method call: ${call.method}');
        }
      } catch (e) {
        debugPrint('Error handling method call: $e');
      }
    });
  }
  
  // Permission methods
  Future<bool> checkOverlayPermission() async {
    try {
      final result = await _channel.invokeMethod<bool>('checkOverlayPermission');
      return result ?? false;
    } catch (e) {
      debugPrint('Error checking overlay permission: $e');
      return false;
    }
  }
  
  Future<void> requestOverlayPermission() async {
    try {
      await _channel.invokeMethod('requestOverlayPermission');
    } catch (e) {
      debugPrint('Error requesting overlay permission: $e');
    }
  }
  
  Future<bool> checkAccessibilityPermission() async {
    try {
      final result = await _channel.invokeMethod<bool>('checkAccessibilityPermission');
      return result ?? false;
    } catch (e) {
      debugPrint('Error checking accessibility permission: $e');
      return false;
    }
  }
  
  Future<void> requestAccessibilityPermission() async {
    try {
      await _channel.invokeMethod('requestAccessibilityPermission');
    } catch (e) {
      debugPrint('Error requesting accessibility permission: $e');
    }
  }
  
  // Service control methods
  Future<void> startOverlayService() async {
    try {
      await _channel.invokeMethod('startOverlayService');
    } catch (e) {
      debugPrint('Error starting overlay service: $e');
    }
  }
  
  Future<void> stopOverlayService() async {
    try {
      await _channel.invokeMethod('stopOverlayService');
    } catch (e) {
      debugPrint('Error stopping overlay service: $e');
    }
  }
  
  // Translation overlay methods
  Future<void> showTranslationOverlay(Map<String, dynamic> message) async {
    try {
      await _channel.invokeMethod('showTranslationOverlay', {
        'message': message,
      });
    } catch (e) {
      debugPrint('Error showing translation overlay: $e');
    }
  }
  
  Future<void> hideTranslationOverlay(String messageId) async {
    try {
      await _channel.invokeMethod('hideTranslationOverlay', {
        'messageId': messageId,
      });
    } catch (e) {
      debugPrint('Error hiding translation overlay: $e');
    }
  }
  
  // Text injection method
  Future<bool> injectText(String text) async {
    try {
      final result = await _channel.invokeMethod<bool>('injectText', {
        'text': text,
      });
      return result ?? false;
    } catch (e) {
      debugPrint('Error injecting text: $e');
      return false;
    }
  }
  
  // Get installed chat apps
  Future<List<Map<String, String>>> getInstalledChatApps() async {
    try {
      final result = await _channel.invokeMethod<List>('getInstalledChatApps');
      return result?.cast<Map<String, String>>() ?? [];
    } catch (e) {
      debugPrint('Error getting installed chat apps: $e');
      return [];
    }
  }
  
  void dispose() {
    _messageDetectedController.close();
    _accessibilityStatusController.close();
    _appFocusController.close();
    _translationDisplayedController.close();
  }
}
