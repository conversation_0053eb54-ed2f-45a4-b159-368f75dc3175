// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a hi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'hi';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts": MessageLookupByLibrary.simpleMessage(
            "अनुवादक को कृत्रिम बुद्धिमत्ता का उपयोग करके पाठ का स्वचालित रूप से अनुवाद करने और चयनित भाषा में भेजने की अनुमति दें।"),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "अनुवादक को कृत्रिम बुद्धिमत्ता का उपयोग करके आपकी ऑडियो रिकॉर्डिंग का स्वचालित रूप से अनुवाद करने और चयनित भाषा में भेजने की अनुमति दें।"),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "फोन ऐप्स तक पहुंच की अनुमति दें।"),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "अनुवादक के साथ तत्काल और सुचारू अनुवाद अनुभव का आनंद लें, जहाँ आप आसानी से और तुरंत पाठ, शब्द और ऑडियो रिकॉर्डिंग को अपनी पसंदीदा भाषा में बदल सकते हैं। उन्नत कृत्रिम बुद्धिमत्ता प्रौद्योगिकियों के लिए धन्यवाद जो उच्च सटीकता और आरामदायक अनुभव सुनिश्चित करती हैं।"),
        "enable_camera": MessageLookupByLibrary.simpleMessage(
            "अनुवादक को कैमरा उपयोग करने की अनुमति दें"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage(
            "अनुवादक को माइक्रोफोन उपयोग करने की अनुमति दें"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "अनुवादक को गैलरी से फाइलें आयात करने की अनुमति दें"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "अनुवादक को गैलरी से छवियाँ आयात करने की अनुमति दें"),
        "language": MessageLookupByLibrary.simpleMessage("भाषा"),
        "live_translation":
            MessageLookupByLibrary.simpleMessage("यहाँ अनुवाद लिखें..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "कृत्रिम बुद्धिमत्ता का उपयोग करके ऑडियो रिकॉर्डिंग को अनुवादित रिकॉर्डिंग में बदलें।"),
        "name_app": MessageLookupByLibrary.simpleMessage("अनुवादक"),
        "search_language":
            MessageLookupByLibrary.simpleMessage("भाषा खोजें..."),
        "symbol_appears_on_the_screen": MessageLookupByLibrary.simpleMessage(
            "प्रतीक स्क्रीन पर दिखाई देता है"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "कृत्रिम बुद्धिमत्ता की शक्ति के साथ अनुवादक के साथ तुरंत अनुवाद करें।"),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "अब पाठ का अनुवाद करने के लिए अनुवादक का उपयोग करें।")
      };
}
