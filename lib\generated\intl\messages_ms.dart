// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ms locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ms';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts": MessageLookupByLibrary.simpleMessage(
            "Benarkan Penterjemah untuk secara automatik menterjemah teks dan menghantarnya dalam bahasa yang dipilih menggunakan kecerdasan buatan."),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "Benarkan Penterjemah untuk secara automatik menterjemah rakaman audio anda dan menghantarnya dalam bahasa yang dipilih menggunakan kecerdasan buatan."),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "Benarkan akses kepada aplikasi telefon."),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "Nikmati pengalaman terjemahan segera dan lancar dengan Penterjemah, di mana anda boleh dengan mudah dan pantas menukar teks, perkataan dan rakaman audio kepada bahasa pilihan anda. Berkat teknologi kecerdasan buatan canggih yang menjamin ketepatan tinggi dan pengalaman yang selesa."),
        "enable_camera": MessageLookupByLibrary.simpleMessage(
            "Benarkan Penterjemah menggunakan kamera"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage(
            "Benarkan Penterjemah menggunakan mikrofon"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "Benarkan Penterjemah mengimport fail dari galeri"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "Benarkan Penterjemah mengimport imej dari galeri"),
        "language": MessageLookupByLibrary.simpleMessage("Bahasa"),
        "live_translation":
            MessageLookupByLibrary.simpleMessage("Tulis terjemahan di sini..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "Tukar rakaman audio kepada rakaman terjemahan menggunakan kecerdasan buatan."),
        "name_app": MessageLookupByLibrary.simpleMessage("Penterjemah"),
        "search_language":
            MessageLookupByLibrary.simpleMessage("Cari bahasa..."),
        "symbol_appears_on_the_screen":
            MessageLookupByLibrary.simpleMessage("Simbol muncul di skrin"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "Terjemahkan serta-merta dengan Penterjemah, berkat kuasa kecerdasan buatan."),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "Gunakan Penterjemah untuk menterjemah teks sekarang.")
      };
}
