// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a de locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'de';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts": MessageLookupByLibrary.simpleMessage(
            "Erlauben Sie Der Übersetzer, Texte automatisch zu übersetzen und sie in der ausgewählten Sprache mit künstlicher Intelligenz zu senden."),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "Erlauben Sie Der Übersetzer, Ihre Audioaufnahmen automatisch zu übersetzen und sie in der ausgewählten Sprache mit künstlicher Intelligenz zu senden."),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "Zugriff auf Telefon-Apps erlauben."),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "Genießen Sie eine sofortige und reibungslose Übersetzungserfahrung mit Der Übersetzer, wo Sie einfach und schnell Texte, Wörter und Audioaufnahmen in Ihre bevorzugte Sprache umwandeln können. Dank fortschrittlicher KI-Technologien, die hohe Genauigkeit und eine komfortable Erfahrung gewährleisten."),
        "enable_camera": MessageLookupByLibrary.simpleMessage(
            "Der Übersetzer darf die Kamera verwenden"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage(
            "Der Übersetzer darf das Mikrofon verwenden"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "Der Übersetzer darf Dateien aus der Galerie importieren"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "Der Übersetzer darf Bilder aus der Galerie importieren"),
        "language": MessageLookupByLibrary.simpleMessage("Sprache"),
        "live_translation": MessageLookupByLibrary.simpleMessage(
            "Schreiben Sie die Übersetzung hier..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "Konvertieren Sie Audioaufnahmen in übersetzte Aufnahmen mit künstlicher Intelligenz."),
        "name_app": MessageLookupByLibrary.simpleMessage("Der Übersetzer"),
        "search_language":
            MessageLookupByLibrary.simpleMessage("Sprache suchen..."),
        "symbol_appears_on_the_screen": MessageLookupByLibrary.simpleMessage(
            "Das Symbol erscheint auf dem Bildschirm"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "Übersetzen Sie sofort mit Der Übersetzer, dank der Kraft der künstlichen Intelligenz."),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "Verwenden Sie Der Übersetzer, um Texte jetzt zu übersetzen.")
      };
}
