import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../cubit/realtime_translation_cubit.dart';

class PermissionsSetupPage extends StatelessWidget {
  const PermissionsSetupPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Permissions Setup'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: BlocBuilder<RealtimeTranslationCubit, RealtimeTranslationState>(
        builder: (context, state) {
          if (state is RealtimeTranslationLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          
          if (state is RealtimeTranslationError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, size: 64.r, color: Colors.red),
                  SizedBox(height: 16.h),
                  Text(
                    state.message,
                    style: TextStyle(fontSize: 16.sp),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16.h),
                  ElevatedButton(
                    onPressed: () => context.read<RealtimeTranslationCubit>().loadConfiguration(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }
          
          if (state is RealtimeTranslationLoaded) {
            return _buildPermissionsContent(context, state);
          }
          
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildPermissionsContent(BuildContext context, RealtimeTranslationLoaded state) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeaderSection(context),
          SizedBox(height: 24.h),
          _buildPermissionCard(
            context,
            title: 'Overlay Permission',
            description: 'Required to display translation overlays on top of other apps',
            icon: Icons.layers,
            isGranted: state.hasOverlayPermission,
            onRequest: () => context.read<RealtimeTranslationCubit>().requestOverlayPermission(),
          ),
          SizedBox(height: 16.h),
          _buildPermissionCard(
            context,
            title: 'Accessibility Service',
            description: 'Required to read text from chat applications for translation',
            icon: Icons.accessibility,
            isGranted: state.isAccessibilityServiceEnabled,
            onRequest: () => context.read<RealtimeTranslationCubit>().requestAccessibilityPermission(),
          ),
          SizedBox(height: 32.h),
          _buildSetupInstructions(context, state),
          SizedBox(height: 32.h),
          _buildContinueButton(context, state),
        ],
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.security, size: 32.r, color: Theme.of(context).primaryColor),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                'Setup Required Permissions',
                style: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Text(
          'Al Tarjuman needs special permissions to provide real-time translation overlays. These permissions are essential for the app to function properly.',
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.grey[600],
            height: 1.4,
          ),
        ),
      ],
    );
  }

  Widget _buildPermissionCard(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required bool isGranted,
    required VoidCallback onRequest,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: isGranted ? Colors.green.withOpacity(0.1) : Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    icon,
                    color: isGranted ? Colors.green : Colors.orange,
                    size: 24.r,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Row(
                        children: [
                          Icon(
                            isGranted ? Icons.check_circle : Icons.warning,
                            color: isGranted ? Colors.green : Colors.orange,
                            size: 16.r,
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            isGranted ? 'Granted' : 'Required',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: isGranted ? Colors.green : Colors.orange,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Text(
              description,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
                height: 1.3,
              ),
            ),
            if (!isGranted) ...[
              SizedBox(height: 16.h),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: onRequest,
                  icon: Icon(Icons.settings, size: 18.r),
                  label: const Text('Grant Permission'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSetupInstructions(BuildContext context, RealtimeTranslationLoaded state) {
    if (state.hasRequiredPermissions) {
      return Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: Colors.green.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 24.r),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                'All required permissions have been granted! You can now use real-time translation.',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.green[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info, color: Colors.blue, size: 24.r),
              SizedBox(width: 12.w),
              Expanded(
                child: Text(
                  'Setup Instructions',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue[700],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            '1. Tap "Grant Permission" for Accessibility Service\n'
            '2. Find "Al Tarjuman" in the accessibility settings\n'
            '3. Turn on the service and confirm\n'
            '4. Return to this app to continue setup',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.blue[600],
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContinueButton(BuildContext context, RealtimeTranslationLoaded state) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: state.hasRequiredPermissions 
            ? () => Navigator.of(context).pop(true)
            : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 16.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
        child: Text(
          'Continue',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
