// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a pt locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'pt';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts": MessageLookupByLibrary.simpleMessage(
            "Permita que O tradutor traduza automaticamente textos e os envie no idioma selecionado usando inteligência artificial."),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "Permita que O tradutor traduza automaticamente suas gravações de áudio e as envie no idioma selecionado usando inteligência artificial."),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "Permitir acesso aos aplicativos do telefone."),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "Desfrute de uma experiência de tradução instantânea e fluida com O tradutor, onde você pode converter facilmente e rapidamente textos, palavras e gravações de áudio para seu idioma preferido. Graças às tecnologias avançadas de inteligência artificial que garantem alta precisão e uma experiência confortável."),
        "enable_camera": MessageLookupByLibrary.simpleMessage(
            "Permitir que O tradutor use a câmera"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage(
            "Permitir que O tradutor use o microfone"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "Permitir que O tradutor importe arquivos da galeria"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "Permitir que O tradutor importe imagens da galeria"),
        "language": MessageLookupByLibrary.simpleMessage("Idioma"),
        "live_translation":
            MessageLookupByLibrary.simpleMessage("Escreva a tradução aqui..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "Converta gravações de áudio em gravação traduzida usando inteligência artificial."),
        "name_app": MessageLookupByLibrary.simpleMessage("O tradutor"),
        "search_language":
            MessageLookupByLibrary.simpleMessage("Pesquisar idioma..."),
        "symbol_appears_on_the_screen":
            MessageLookupByLibrary.simpleMessage("O símbolo aparece na tela"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "Traduza instantaneamente com O tradutor, graças ao poder da inteligência artificial."),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "Use O tradutor para traduzir textos agora.")
      };
}
