package com.example.al_tarjuman.overlay

import android.app.Service
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import com.example.al_tarjuman.R
import com.example.al_tarjuman.models.ChatMessage

class OverlayService : Service() {
    
    companion object {
        private const val TAG = "OverlayService"
        var instance: OverlayService? = null
    }
    
    private lateinit var windowManager: WindowManager
    private val overlayViews = mutableMapOf<String, View>()
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        Log.d(TAG, "OverlayService created")
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "OverlayService started")
        return START_STICKY
    }
    
    fun showTranslationOverlay(message: ChatMessage) {
        if (message.translatedText.isNullOrBlank()) return
        
        try {
            // Remove existing overlay for this message if any
            removeOverlay(message.id)
            
            // Create overlay view
            val overlayView = createOverlayView(message)
            val layoutParams = createOverlayLayoutParams(message)
            
            // Add to window manager
            windowManager.addView(overlayView, layoutParams)
            overlayViews[message.id] = overlayView
            
            // Auto-hide after 5 seconds
            overlayView.postDelayed({
                removeOverlay(message.id)
            }, 5000)
            
            Log.d(TAG, "Translation overlay shown for message: ${message.id}")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to show translation overlay", e)
        }
    }
    
    fun removeOverlay(messageId: String) {
        overlayViews[messageId]?.let { view ->
            try {
                windowManager.removeView(view)
                overlayViews.remove(messageId)
                Log.d(TAG, "Overlay removed for message: $messageId")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to remove overlay", e)
            }
        }
    }
    
    fun removeAllOverlays() {
        overlayViews.keys.toList().forEach { messageId ->
            removeOverlay(messageId)
        }
    }
    
    private fun createOverlayView(message: ChatMessage): View {
        val inflater = LayoutInflater.from(this)
        val overlayView = inflater.inflate(R.layout.overlay_translation, null)
        
        val translationText = overlayView.findViewById<TextView>(R.id.translationText)
        val originalText = overlayView.findViewById<TextView>(R.id.originalText)
        val closeButton = overlayView.findViewById<View>(R.id.closeButton)
        
        translationText.text = message.translatedText
        originalText.text = message.text
        
        // Set background color based on message direction
        val backgroundColor = if (message.isIncoming) {
            0xCC2196F3.toInt() // Blue for incoming
        } else {
            0xCC4CAF50.toInt() // Green for outgoing
        }
        overlayView.setBackgroundColor(backgroundColor)
        
        // Close button functionality
        closeButton.setOnClickListener {
            removeOverlay(message.id)
        }
        
        return overlayView
    }
    
    private fun createOverlayLayoutParams(message: ChatMessage): WindowManager.LayoutParams {
        val layoutParams = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_PHONE
            },
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
            PixelFormat.TRANSLUCENT
        )
        
        // Position overlay near the original message
        layoutParams.gravity = Gravity.TOP or Gravity.START
        layoutParams.x = message.bounds.left
        layoutParams.y = message.bounds.bottom + 10 // Slightly below the original message
        
        return layoutParams
    }
    
    override fun onDestroy() {
        super.onDestroy()
        removeAllOverlays()
        instance = null
        Log.d(TAG, "OverlayService destroyed")
    }
}
