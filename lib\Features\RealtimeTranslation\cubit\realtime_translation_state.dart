part of 'realtime_translation_cubit.dart';

@immutable
abstract class RealtimeTranslationState {}

class RealtimeTranslationInitial extends RealtimeTranslationState {}

class RealtimeTranslationLoading extends RealtimeTranslationState {}

class RealtimeTranslationLoaded extends RealtimeTranslationState {
  final bool isServiceEnabled;
  final bool hasOverlayPermission;
  final bool isAccessibilityServiceEnabled;
  final String targetLanguage;
  final String sourceLanguage;
  final bool autoTranslateIncoming;
  final bool autoTranslateOutgoing;
  final Set<String> enabledApps;
  final List<Map<String, String>> installedChatApps;
  final String? currentFocusedApp;
  final Map<String, Map<String, dynamic>> activeTranslations;
  final Map<String, dynamic>? lastTranslationUpdate;

  const RealtimeTranslationLoaded({
    required this.isServiceEnabled,
    required this.hasOverlayPermission,
    required this.isAccessibilityServiceEnabled,
    required this.targetLanguage,
    required this.sourceLanguage,
    required this.autoTranslateIncoming,
    required this.autoTranslateOutgoing,
    required this.enabledApps,
    required this.installedChatApps,
    required this.currentFocusedApp,
    required this.activeTranslations,
    required this.lastTranslationUpdate,
  });

  RealtimeTranslationLoaded copyWith({
    bool? isServiceEnabled,
    bool? hasOverlayPermission,
    bool? isAccessibilityServiceEnabled,
    String? targetLanguage,
    String? sourceLanguage,
    bool? autoTranslateIncoming,
    bool? autoTranslateOutgoing,
    Set<String>? enabledApps,
    List<Map<String, String>>? installedChatApps,
    String? currentFocusedApp,
    Map<String, Map<String, dynamic>>? activeTranslations,
    Map<String, dynamic>? lastTranslationUpdate,
  }) {
    return RealtimeTranslationLoaded(
      isServiceEnabled: isServiceEnabled ?? this.isServiceEnabled,
      hasOverlayPermission: hasOverlayPermission ?? this.hasOverlayPermission,
      isAccessibilityServiceEnabled: isAccessibilityServiceEnabled ?? this.isAccessibilityServiceEnabled,
      targetLanguage: targetLanguage ?? this.targetLanguage,
      sourceLanguage: sourceLanguage ?? this.sourceLanguage,
      autoTranslateIncoming: autoTranslateIncoming ?? this.autoTranslateIncoming,
      autoTranslateOutgoing: autoTranslateOutgoing ?? this.autoTranslateOutgoing,
      enabledApps: enabledApps ?? this.enabledApps,
      installedChatApps: installedChatApps ?? this.installedChatApps,
      currentFocusedApp: currentFocusedApp ?? this.currentFocusedApp,
      activeTranslations: activeTranslations ?? this.activeTranslations,
      lastTranslationUpdate: lastTranslationUpdate ?? this.lastTranslationUpdate,
    );
  }

  bool get canEnableService => hasOverlayPermission && isAccessibilityServiceEnabled;
  
  bool get hasRequiredPermissions => hasOverlayPermission && isAccessibilityServiceEnabled;
  
  List<String> get missingPermissions {
    final missing = <String>[];
    if (!hasOverlayPermission) missing.add('Overlay Permission');
    if (!isAccessibilityServiceEnabled) missing.add('Accessibility Service');
    return missing;
  }
}

class RealtimeTranslationError extends RealtimeTranslationState {
  final String message;

  const RealtimeTranslationError(this.message);
}
