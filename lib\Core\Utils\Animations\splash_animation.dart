import 'package:flutter/material.dart';


class SplashAnimation {
  late final AnimationController _controller;
  late final Animation<double> _fadeAnimation;
  late final Animation<double> _scaleAnimation;
  late final Animation<Offset> _slideAnimation;
  late final Animation<double> _logoSlideAnimation;

  static const Duration animationDuration = Duration(milliseconds: 2500);

  /// Initialize the animation with the provided TickerProvider
  SplashAnimation({required TickerProvider vsync}) {
    _controller = AnimationController(
      duration: animationDuration,
      vsync: vsync,
    );

    // Fade animation for the main splash image (starts immediately)
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.6, curve: Curves.easeInOut),
    ));

    // Scale animation for the splash image (gentle bounce effect)
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.8, curve: Curves.elasticOut),
    ));

    // Slide animation for the splash image (from bottom)
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.7, curve: Curves.easeOutCubic),
    ));

    // Logo slide animation (delayed entrance from right)
    _logoSlideAnimation = Tween<double>(
      begin: 50.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.4, 1.0, curve: Curves.easeOutBack),
    ));
  }

  /// Start the splash animation
  void startAnimation() {
    _controller.forward();
    
  }

  /// Reset the animation to its initial state
  void resetAnimation() {
    _controller.reset();
  }

  /// Dispose of the animation controller to prevent memory leaks
  void dispose() {
    _controller.dispose();
  }

  /// Get the animation controller for external listeners
  AnimationController get controller => _controller;

  /// Animated builder for the main splash image with fade, scale, and slide effects
  Widget buildAnimatedSplashImage({required Widget child}) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, _) {
        return SlideTransition(
          position: _slideAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: child,
            ),
          ),
        );
      },
    );
  }

  /// Animated builder for the app name and logo row with slide effect
  Widget buildAnimatedAppNameRow({required Widget child}) {
    return AnimatedBuilder(
      animation: _logoSlideAnimation,
      builder: (context, _) {
        return Transform.translate(
          offset: Offset(_logoSlideAnimation.value, 0),
          child: FadeTransition(
            opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
              CurvedAnimation(
                parent: _controller,
                curve: const Interval(0.5, 1.0, curve: Curves.easeInOut),
              ),
            ),
            child: child,
          ),
        );
      },
    );
  }

  bool get isCompleted => _controller.isCompleted;

  bool get isAnimating => _controller.isAnimating;

  double get progress => _controller.value;
}
