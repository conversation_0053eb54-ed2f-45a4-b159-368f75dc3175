<?xml version="1.0" encoding="utf-8"?>
<accessibility-service xmlns:android="http://schemas.android.com/apk/res/android"
    android:accessibilityEventTypes="typeWindowStateChanged|typeWindowContentChanged|typeViewTextChanged"
    android:accessibilityFeedbackType="feedbackGeneric"
    android:accessibilityFlags="flagDefault|flagRetrieveInteractiveWindows|flagReportViewIds"
    android:canRetrieveWindowContent="true"
    android:canRequestTouchExplorationMode="false"
    android:canRequestEnhancedWebAccessibility="false"
    android:canRequestFilterKeyEvents="false"
    android:notificationTimeout="100"
    android:packageNames="com.whatsapp,com.whatsapp.w4b,org.telegram.messenger,com.facebook.orca,com.viber.voip,com.skype.raider,com.snapchat.android,com.instagram.android,com.twitter.android,com.discord"
    android:description="@string/accessibility_service_description"
    android:summary="@string/accessibility_service_summary" />
