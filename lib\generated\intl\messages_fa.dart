// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a fa locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'fa';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts": MessageLookupByLibrary.simpleMessage(
            "به مترجم اجازه دهید تا با استفاده از هوش مصنوعی، متون را به طور خودکار ترجمه کرده و به زبان انتخابی ارسال کند."),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "به مترجم اجازه دهید تا با استفاده از هوش مصنوعی، ضبط‌های صوتی شما را به طور خودکار ترجمه کرده و به زبان انتخابی ارسال کند."),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "اجازه دسترسی به برنامه‌های تلفن را بدهید."),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "از تجربه ترجمه فوری و روان با مترجم لذت ببرید، جایی که می‌توانید به راحتی و سرعت متون، کلمات و ضبط‌های صوتی را به زبان مورد علاقه خود تبدیل کنید. به لطف فناوری‌های پیشرفته هوش مصنوعی که دقت بالا و تجربه راحت را تضمین می‌کنند."),
        "enable_camera": MessageLookupByLibrary.simpleMessage(
            "به مترجم اجازه استفاده از دوربین را بدهید"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage(
            "به مترجم اجازه استفاده از میکروفون را بدهید"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "به مترجم اجازه واردکردن فایل‌ها از گالری را بدهید"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "به مترجم اجازه واردکردن تصاویر از گالری را بدهید"),
        "language": MessageLookupByLibrary.simpleMessage("زبان"),
        "live_translation":
            MessageLookupByLibrary.simpleMessage("ترجمه را اینجا بنویسید..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "ضبط‌های صوتی را با استفاده از هوش مصنوعی به ضبط ترجمه شده تبدیل کنید."),
        "name_app": MessageLookupByLibrary.simpleMessage("مترجم"),
        "search_language":
            MessageLookupByLibrary.simpleMessage("جستجوی زبان..."),
        "symbol_appears_on_the_screen":
            MessageLookupByLibrary.simpleMessage("نماد روی صفحه ظاهر می‌شود"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "با قدرت هوش مصنوعی، با مترجم فوری ترجمه کنید."),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "اکنون از مترجم برای ترجمه متون استفاده کنید.")
      };
}
