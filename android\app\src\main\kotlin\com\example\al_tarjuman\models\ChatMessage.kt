package com.example.al_tarjuman.models

import android.graphics.Rect

data class ChatMessage(
    val id: String,
    val text: String,
    val bounds: Rect,
    val isIncoming: Boolean,
    val timestamp: Long,
    val appPackage: String,
    var translatedText: String? = null,
    var isTranslated: Boolean = false
) {
    fun toMap(): Map<String, Any> {
        return mapOf(
            "id" to id,
            "text" to text,
            "bounds" to mapOf(
                "left" to bounds.left,
                "top" to bounds.top,
                "right" to bounds.right,
                "bottom" to bounds.bottom
            ),
            "isIncoming" to isIncoming,
            "timestamp" to timestamp,
            "appPackage" to appPackage,
            "translatedText" to (translatedText ?: ""),
            "isTranslated" to isTranslated
        )
    }
    
    companion object {
        fun fromMap(map: Map<String, Any>): ChatMessage {
            val boundsMap = map["bounds"] as Map<String, Int>
            val bounds = Rect(
                boundsMap["left"] ?: 0,
                boundsMap["top"] ?: 0,
                boundsMap["right"] ?: 0,
                boundsMap["bottom"] ?: 0
            )
            
            return ChatMessage(
                id = map["id"] as String,
                text = map["text"] as String,
                bounds = bounds,
                isIncoming = map["isIncoming"] as Boolean,
                timestamp = map["timestamp"] as Long,
                appPackage = map["appPackage"] as String,
                translatedText = map["translatedText"] as? String,
                isTranslated = map["isTranslated"] as? Boolean ?: false
            )
        }
    }
}
