// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts":
            MessageLookupByLibrary.simpleMessage("允许翻译器使用人工智能自动翻译文本并以选定的语言发送。"),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "允许翻译器使用人工智能自动翻译您的音频录音并以选定的语言发送。"),
        "chat_now": MessageLookupByLibrary.simpleMessage("允许访问手机应用程序。"),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "享受翻译器带来的即时流畅翻译体验，您可以轻松快速地将文本、单词和音频录音转换为您的首选语言。借助先进的人工智能技术，确保高精度和舒适的体验。"),
        "enable_camera": MessageLookupByLibrary.simpleMessage("允许翻译器使用相机"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage("允许翻译器使用麦克风"),
        "enable_pick_file":
            MessageLookupByLibrary.simpleMessage("允许翻译器从图库导入文件"),
        "enable_pick_image":
            MessageLookupByLibrary.simpleMessage("允许翻译器从图库导入图像"),
        "language": MessageLookupByLibrary.simpleMessage("语言"),
        "live_translation": MessageLookupByLibrary.simpleMessage("在此处写入翻译..."),
        "micro_now":
            MessageLookupByLibrary.simpleMessage("使用人工智能将音频录音转换为翻译录音。"),
        "name_app": MessageLookupByLibrary.simpleMessage("翻译器"),
        "search_language": MessageLookupByLibrary.simpleMessage("搜索语言..."),
        "symbol_appears_on_the_screen":
            MessageLookupByLibrary.simpleMessage("符号出现在屏幕上"),
        "title_card":
            MessageLookupByLibrary.simpleMessage("借助人工智能的力量，使用翻译器即时翻译。"),
        "translate_now": MessageLookupByLibrary.simpleMessage("现在使用翻译器翻译文本。")
      };
}
