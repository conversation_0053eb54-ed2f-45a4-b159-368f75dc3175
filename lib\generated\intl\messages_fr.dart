// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a fr locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'fr';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts": MessageLookupByLibrary.simpleMessage(
            "Permettez à Le traducteur de traduire automatiquement les textes et de les envoyer dans la langue sélectionnée en utilisant l\'intelligence artificielle."),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "Permettez à Le traducteur de traduire automatiquement vos enregistrements audio et de les envoyer dans la langue sélectionnée en utilisant l\'intelligence artificielle."),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "Permettre l\'accès aux applications du téléphone."),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "Profitez d\'une expérience de traduction instantanée et fluide avec Le traducteur, où vous pouvez facilement et rapidement convertir des textes, des mots et des enregistrements audio dans votre langue préférée. Grâce aux technologies d\'intelligence artificielle avancées qui garantissent une haute précision et une expérience confortable."),
        "enable_camera": MessageLookupByLibrary.simpleMessage(
            "Permettre à Le traducteur d\'utiliser la caméra"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage(
            "Permettre à Le traducteur d\'utiliser le microphone"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "Permettre à Le traducteur d\'importer des fichiers depuis la galerie"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "Permettre à Le traducteur d\'importer des images depuis la galerie"),
        "language": MessageLookupByLibrary.simpleMessage("Langue"),
        "live_translation": MessageLookupByLibrary.simpleMessage(
            "Écrivez la traduction ici..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "Convertissez les enregistrements audio en enregistrement traduit en utilisant l\'intelligence artificielle."),
        "name_app": MessageLookupByLibrary.simpleMessage("Le traducteur"),
        "search_language":
            MessageLookupByLibrary.simpleMessage("Rechercher une langue..."),
        "symbol_appears_on_the_screen": MessageLookupByLibrary.simpleMessage(
            "Le symbole apparaît à l\'écran"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "Traduisez instantanément avec Le traducteur, grâce à la puissance de l\'intelligence artificielle."),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "Utilisez Le traducteur pour traduire des textes maintenant.")
      };
}
