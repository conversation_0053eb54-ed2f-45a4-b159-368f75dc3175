// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a es locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'es';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts": MessageLookupByLibrary.simpleMessage(
            "Permite a El traductor traducir automáticamente textos y enviarlos en el idioma seleccionado usando inteligencia artificial."),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "Permite a El traductor traducir automáticamente tus grabaciones de audio y enviarlas en el idioma seleccionado usando inteligencia artificial."),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "Permitir acceso a las aplicaciones del teléfono."),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "Disfruta de una experiencia de traducción instantánea y fluida con El traductor, donde puedes convertir fácil y rápidamente textos, palabras y grabaciones de audio a tu idioma preferido. Gracias a las tecnologías avanzadas de inteligencia artificial que garantizan alta precisión y una experiencia cómoda."),
        "enable_camera": MessageLookupByLibrary.simpleMessage(
            "Permitir a El traductor usar la cámara"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage(
            "Permitir a El traductor usar el micrófono"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "Permitir a El traductor importar archivos desde la galería"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "Permitir a El traductor importar imágenes desde la galería"),
        "language": MessageLookupByLibrary.simpleMessage("Idioma"),
        "live_translation": MessageLookupByLibrary.simpleMessage(
            "Escribe la traducción aquí..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "Convierte grabaciones de audio en grabación traducida usando inteligencia artificial."),
        "name_app": MessageLookupByLibrary.simpleMessage("El traductor"),
        "search_language":
            MessageLookupByLibrary.simpleMessage("Buscar idioma..."),
        "symbol_appears_on_the_screen": MessageLookupByLibrary.simpleMessage(
            "El símbolo aparece en la pantalla"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "Traduce instantáneamente con El traductor, gracias al poder de la inteligencia artificial."),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "Usa El traductor para traducir textos ahora.")
      };
}
