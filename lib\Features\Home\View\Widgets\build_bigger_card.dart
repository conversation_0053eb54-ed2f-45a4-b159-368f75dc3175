import '../../../../Core/Resources/text_style.dart';
import '../../../../Core/Utils/Enum/type_cards.dart';
import '../../../../Core/Utils/Extensions/localizations_extension.dart';
import '../../../../Core/Utils/Widget/build_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildBiggerCard extends StatelessWidget {
  const BuildBiggerCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: TypeCards.title.height,
      width: TypeCards.title.width.sw,

      decoration: BoxDecoration(
        color: Color(TypeCards.title.valueColors),
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: Color(TypeCards.title.valueColors).withAlpha(50),
            spreadRadius: 0,
            blurRadius: 10,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Stack(
        children: [
          Align(
            alignment: context.isRtl ? Alignment.topRight : Alignment.topLeft,
            child: SizedBox(
              width: 200.w,
              child: Padding(
                padding: EdgeInsets.only(
                  top: 20.h,
                  left: 20.w,
                  right: 20.w,
                  bottom: 20.h,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.local.title_card,
                      maxLines: context.local.title_card.length,
                      style: AppTextStyles.h7Bold.copyWith(color: Colors.white),
                    ),
                    Spacer(),
                    BackButton(),
                  ],
                ),
              ),
            ),
          ),
          Align(
            alignment: context.isRtl
                ? Alignment.centerLeft
                : Alignment.centerRight,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: BuildImageAssets(svg: TypeCards.title.image, width: TypeCards.title.widthImage.w),
            ),
          ),
        ],
      ),
    );
  }
}
