import '../../../../Core/Storage/Models/language_mode.dart';

import '../../../../Core/Resources/text_style.dart';
import '../../../../Core/Utils/Extensions/localizations_extension.dart';
import 'package:flutter/material.dart';

class LanguagePicker extends StatefulWidget {
  final List<LanguageMode> languages;
  final void Function(LanguageMode language) onSelected;

  const LanguagePicker({
    super.key,
    required this.languages,
    required this.onSelected,
  });

  @override
  State<LanguagePicker> createState() => _LanguagePickerState();
}

class _LanguagePickerState extends State<LanguagePicker> {
  late List<LanguageMode> filteredLanguages;
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    filteredLanguages = widget.languages;
    searchController.addListener(_filterLanguages);
  }

  void _filterLanguages() {
    final query = searchController.text.toLowerCase();
    setState(() {
      filteredLanguages = widget.languages
          .where((lang) => lang.languageName.toLowerCase().contains(query))
          .toList();
    });
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(20),
          child: TextField(
            controller: searchController,
            decoration: InputDecoration(
              prefixIcon: const Icon(Icons.search),
              hintText: context.local.search_language,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: filteredLanguages.length,
            itemBuilder: (context, index) {
              final language = filteredLanguages[index];
              return ListTile(
                leading: language.flag,
                title: Text(
                  language.languageName,
                  style: AppTextStyles.bodyLargeSemiBold,
                ),
                onTap: () => widget.onSelected(language),
              );
            },
          ),
        ),
      ],
    );
  }
}
