// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts": MessageLookupByLibrary.simpleMessage(
            "Allow The translator to translate texts automatically and send them in the selected language using artificial intelligence."),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "Allow The translator to translate your audio recordings automatically and send them in the selected language using artificial intelligence."),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "Chat with The translator now."),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "Enjoy a smooth and instant translation experience with The translator, where you can easily and quickly translate texts, words, and audio recordings into your preferred language. Thanks to the advanced artificial intelligence technologies that ensure high accuracy and a pleasant user experience."),
        "enable_camera": MessageLookupByLibrary.simpleMessage(
            "Allow The translator to use the camera"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage(
            "Allow The translator to use the microphone"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "Allow The translator to import files from the gallery"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "Allow The translator to import images from the gallery"),
        "language": MessageLookupByLibrary.simpleMessage("Language"),
        "live_translation": MessageLookupByLibrary.simpleMessage(
            "Write the translation here..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "Use The translator to translate audio now."),
        "name_app": MessageLookupByLibrary.simpleMessage("The translator"),
        "search_language":
            MessageLookupByLibrary.simpleMessage("Search language..."),
        "symbol_appears_on_the_screen": MessageLookupByLibrary.simpleMessage(
            "The symbol appears on the screen"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "Translate instantly with The translator, thanks to the power of artificial intelligence."),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "Use The translator to translate texts now.")
      };
}
