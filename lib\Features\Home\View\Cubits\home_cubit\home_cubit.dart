
import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';

part 'home_state.dart';

class HomeCubit extends Cubit<HomeState> {
  HomeCubit() : super(HomeState());
   final TextEditingController controller = TextEditingController();
  void init() {
    initLanguageTrasnlate();
  }
  void initLanguageTrasnlate() {
    if (state.languageFirstTranslate == null &&
        state.languageSecondTranslate == null) {
      emit(
        state.copyWith(
          languageFirstTranslate:"English",
        ),
      );
    }
    if (state.languageSecondTranslate == null) {
      emit(
        state.copyWith(
          languageSecondTranslate:"عربية",
        ),
      );
    }

  }

 

  void changeLanguage(bool isFirst, String languageCode) {
    if (isFirst) {
      emit(
        state.copyWith(languageFirstTranslate: (languageCode)),
      );
    } else {
      emit(
        state.copyWith(languageSecondTranslate: (languageCode)),
      );
    }
  }

  void repeatedLanguage() {
    final firstLanguage = state.languageFirstTranslate;
    final secondLanguage = state.languageSecondTranslate;
    emit(
      state.copyWith(
        languageSecondTranslate: firstLanguage,
        languageFirstTranslate: secondLanguage,
      ),
    );
  }
}
